package com.mahmoudffyt.Gfx_booster.tools;

import android.app.ActivityManager;
import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import com.mahmoudffyt.Gfx_booster.R;
import com.mahmoudffyt.Gfx_booster.utils.DeviceInfoManager;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class ClearCacheActivity extends AppCompatActivity {

    private TextView cacheInfoText;
    private TextView ramInfoText;
    private Button clearCacheButton;
    private ProgressBar progressBar;
    private DeviceInfoManager deviceInfoManager;
    private ExecutorService executor;
    private Handler mainHandler;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_clear_cache);

        setupToolbar();
        initViews();
        initManagers();
        updateInfo();
    }

    private void setupToolbar() {
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("مسح التخزين المؤقت");
        }
    }

    private void initViews() {
        cacheInfoText = findViewById(R.id.cache_info_text);
        ramInfoText = findViewById(R.id.ram_info_text);
        clearCacheButton = findViewById(R.id.clear_cache_button);
        progressBar = findViewById(R.id.progress_bar);
        
        clearCacheButton.setOnClickListener(v -> startClearCache());
    }

    private void initManagers() {
        deviceInfoManager = new DeviceInfoManager(this);
        executor = Executors.newSingleThreadExecutor();
        mainHandler = new Handler(Looper.getMainLooper());
    }

    private void updateInfo() {
        // Update RAM info
        String ramInfo = "الذاكرة المتاحة: " + deviceInfoManager.getAvailableRAM() + 
                        "\nإجمالي الذاكرة: " + deviceInfoManager.getTotalRAM() +
                        "\nاستخدام الذاكرة: " + deviceInfoManager.getRAMUsagePercentage() + "%";
        ramInfoText.setText(ramInfo);

        // Update cache info
        long cacheSize = getCacheSize();
        String cacheInfo = "حجم التخزين المؤقت: " + formatBytes(cacheSize) +
                          "\nعدد الملفات المؤقتة: " + getCacheFileCount();
        cacheInfoText.setText(cacheInfo);
    }

    private void startClearCache() {
        clearCacheButton.setEnabled(false);
        progressBar.setVisibility(ProgressBar.VISIBLE);
        
        executor.execute(() -> {
            try {
                // Simulate cache clearing process
                for (int i = 0; i <= 100; i += 10) {
                    final int progress = i;
                    mainHandler.post(() -> {
                        progressBar.setProgress(progress);
                        cacheInfoText.setText("جاري مسح التخزين المؤقت... " + progress + "%");
                    });
                    
                    Thread.sleep(200); // Simulate work
                    
                    if (i == 50) {
                        // Clear app cache
                        clearAppCache();
                    }
                }
                
                // Force garbage collection
                System.gc();
                
                mainHandler.post(() -> {
                    progressBar.setVisibility(ProgressBar.GONE);
                    clearCacheButton.setEnabled(true);
                    cacheInfoText.setText("تم مسح التخزين المؤقت بنجاح!");
                    
                    // Update info after clearing
                    new Handler().postDelayed(this::updateInfo, 1000);
                });
                
            } catch (InterruptedException e) {
                mainHandler.post(() -> {
                    progressBar.setVisibility(ProgressBar.GONE);
                    clearCacheButton.setEnabled(true);
                    cacheInfoText.setText("فشل في مسح التخزين المؤقت");
                });
            }
        });
    }

    private void clearAppCache() {
        try {
            // Clear app's internal cache
            deleteDir(getCacheDir());
            
            // Clear external cache if available
            if (getExternalCacheDir() != null) {
                deleteDir(getExternalCacheDir());
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private boolean deleteDir(java.io.File dir) {
        if (dir != null && dir.isDirectory()) {
            String[] children = dir.list();
            if (children != null) {
                for (String child : children) {
                    boolean success = deleteDir(new java.io.File(dir, child));
                    if (!success) {
                        return false;
                    }
                }
            }
            return dir.delete();
        } else if (dir != null && dir.isFile()) {
            return dir.delete();
        } else {
            return false;
        }
    }

    private long getCacheSize() {
        long size = 0;
        try {
            size += getDirSize(getCacheDir());
            if (getExternalCacheDir() != null) {
                size += getDirSize(getExternalCacheDir());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return size;
    }

    private long getDirSize(java.io.File dir) {
        long size = 0;
        if (dir != null && dir.exists()) {
            java.io.File[] files = dir.listFiles();
            if (files != null) {
                for (java.io.File file : files) {
                    if (file.isDirectory()) {
                        size += getDirSize(file);
                    } else {
                        size += file.length();
                    }
                }
            }
        }
        return size;
    }

    private int getCacheFileCount() {
        int count = 0;
        try {
            count += getFileCount(getCacheDir());
            if (getExternalCacheDir() != null) {
                count += getFileCount(getExternalCacheDir());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return count;
    }

    private int getFileCount(java.io.File dir) {
        int count = 0;
        if (dir != null && dir.exists()) {
            java.io.File[] files = dir.listFiles();
            if (files != null) {
                for (java.io.File file : files) {
                    if (file.isDirectory()) {
                        count += getFileCount(file);
                    } else {
                        count++;
                    }
                }
            }
        }
        return count;
    }

    private String formatBytes(long bytes) {
        if (bytes < 1024) return bytes + " B";
        else if (bytes < 1024 * 1024) return String.format("%.1f KB", bytes / 1024.0);
        else if (bytes < 1024 * 1024 * 1024) return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
        else return String.format("%.1f GB", bytes / (1024.0 * 1024.0 * 1024.0));
    }

    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (executor != null) {
            executor.shutdown();
        }
    }
}
