package com.mahmoudffyt.Gfx_booster.tools;

import android.os.Bundle;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import com.mahmoudffyt.Gfx_booster.R;
import com.mahmoudffyt.Gfx_booster.utils.DeviceInfoManager;

public class DeviceInfoActivity extends AppCompatActivity {

    private DeviceInfoManager deviceInfoManager;
    
    // Views
    private TextView deviceModelText;
    private TextView androidVersionText;
    private TextView deviceNameText;
    private TextView totalRamText;
    private TextView availableRamText;
    private TextView totalStorageText;
    private TextView availableStorageText;
    private TextView batteryLevelText;
    private TextView networkTypeText;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_device_info);

        setupToolbar();
        initViews();
        initDeviceInfoManager();
        loadDeviceInfo();
    }

    private void setupToolbar() {
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("معلومات الجهاز");
        }
    }

    private void initViews() {
        deviceModelText = findViewById(R.id.device_model_text);
        androidVersionText = findViewById(R.id.android_version_text);
        deviceNameText = findViewById(R.id.device_name_text);
        totalRamText = findViewById(R.id.total_ram_text);
        availableRamText = findViewById(R.id.available_ram_text);
        totalStorageText = findViewById(R.id.total_storage_text);
        availableStorageText = findViewById(R.id.available_storage_text);
        batteryLevelText = findViewById(R.id.battery_level_text);
        networkTypeText = findViewById(R.id.network_type_text);
    }

    private void initDeviceInfoManager() {
        deviceInfoManager = new DeviceInfoManager(this);
    }

    private void loadDeviceInfo() {
        // Device Information
        deviceModelText.setText(deviceInfoManager.getDeviceModel());
        androidVersionText.setText(deviceInfoManager.getAndroidVersion());
        deviceNameText.setText(deviceInfoManager.getDeviceName());
        
        // Memory Information
        totalRamText.setText(deviceInfoManager.getTotalRAM());
        availableRamText.setText(deviceInfoManager.getAvailableRAM());
        
        // Storage Information
        totalStorageText.setText(deviceInfoManager.getTotalStorage());
        availableStorageText.setText(deviceInfoManager.getAvailableStorage());
        
        // Battery Information
        int batteryLevel = deviceInfoManager.getBatteryLevel();
        String batteryStatus = deviceInfoManager.isBatteryCharging() ? " (شحن)" : "";
        batteryLevelText.setText(batteryLevel + "%" + batteryStatus);
        
        // Network Information
        networkTypeText.setText(deviceInfoManager.getNetworkType());
    }

    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }
}
