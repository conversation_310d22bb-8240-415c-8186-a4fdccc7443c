<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_primary">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary_blue"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    <!-- Content -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Device Information Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                style="@style/CustomCardView"
                android:layout_marginBottom="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="معلومات الجهاز"
                        style="@style/HeadlineText"
                        android:layout_marginBottom="16dp"
                        android:drawableStart="@drawable/ic_device_info"
                        android:drawablePadding="8dp"
                        android:gravity="center_vertical" />

                    <!-- Device Model -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="طراز الجهاز:"
                            style="@style/BodyText" />

                        <TextView
                            android:id="@+id/device_model_text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Samsung Galaxy S21"
                            style="@style/BodyText"
                            android:textColor="@color/text_primary" />

                    </LinearLayout>

                    <!-- Android Version -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="إصدار الأندرويد:"
                            style="@style/BodyText" />

                        <TextView
                            android:id="@+id/android_version_text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Android 13"
                            style="@style/BodyText"
                            android:textColor="@color/text_primary" />

                    </LinearLayout>

                    <!-- Device Name -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="اسم الجهاز:"
                            style="@style/BodyText" />

                        <TextView
                            android:id="@+id/device_name_text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="SM-G991B"
                            style="@style/BodyText"
                            android:textColor="@color/text_primary" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Memory Information Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                style="@style/CustomCardView"
                android:layout_marginBottom="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="معلومات الذاكرة"
                        style="@style/HeadlineText"
                        android:layout_marginBottom="16dp"
                        android:drawableStart="@drawable/ic_tools"
                        android:drawablePadding="8dp"
                        android:gravity="center_vertical" />

                    <!-- Total RAM -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="إجمالي الذاكرة:"
                            style="@style/BodyText" />

                        <TextView
                            android:id="@+id/total_ram_text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="8 GB"
                            style="@style/BodyText"
                            android:textColor="@color/text_primary" />

                    </LinearLayout>

                    <!-- Available RAM -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="الذاكرة المتاحة:"
                            style="@style/BodyText" />

                        <TextView
                            android:id="@+id/available_ram_text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="4.2 GB"
                            style="@style/BodyText"
                            android:textColor="@color/accent_cyan" />

                    </LinearLayout>

                    <!-- Total Storage -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="إجمالي التخزين:"
                            style="@style/BodyText" />

                        <TextView
                            android:id="@+id/total_storage_text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="128 GB"
                            style="@style/BodyText"
                            android:textColor="@color/text_primary" />

                    </LinearLayout>

                    <!-- Available Storage -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="التخزين المتاح:"
                            style="@style/BodyText" />

                        <TextView
                            android:id="@+id/available_storage_text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="45.8 GB"
                            style="@style/BodyText"
                            android:textColor="@color/accent_cyan" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Status Information Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                style="@style/CustomCardView">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="معلومات الحالة"
                        style="@style/HeadlineText"
                        android:layout_marginBottom="16dp"
                        android:drawableStart="@drawable/ic_tips"
                        android:drawablePadding="8dp"
                        android:gravity="center_vertical" />

                    <!-- Battery Level -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="مستوى البطارية:"
                            style="@style/BodyText" />

                        <TextView
                            android:id="@+id/battery_level_text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="85%"
                            style="@style/BodyText"
                            android:textColor="@color/success_green" />

                    </LinearLayout>

                    <!-- Network Type -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="نوع الشبكة:"
                            style="@style/BodyText" />

                        <TextView
                            android:id="@+id/network_type_text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="WiFi"
                            style="@style/BodyText"
                            android:textColor="@color/accent_cyan" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </ScrollView>

</LinearLayout>
