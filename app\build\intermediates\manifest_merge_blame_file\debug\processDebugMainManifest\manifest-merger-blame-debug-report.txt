1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.mahmoudffyt.Gfx_booster"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="28"
9        android:targetSdkVersion="35" />
10
11    <!-- Permissions for device info and ads -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:8:5-76
14-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:8:22-73
15    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
15-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:9:5-78
15-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:9:22-75
16    <uses-permission android:name="android.permission.GET_TASKS" />
16-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:10:5-68
16-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:10:22-65
17    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS" />
17-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:11:5-12:47
17-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:11:22-75
18
19    <!-- AdMob App ID -->
20    <meta-data
20-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:15:5-17:65
21        android:name="com.google.android.gms.ads.APPLICATION_ID"
21-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:16:9-65
22        android:value="ca-app-pub-3940256099942544~3347511713" />
22-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:17:9-63
23
24    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
24-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:26:5-79
24-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:26:22-76
25    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
25-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:27:5-82
25-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:27:22-79
26    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
26-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:28:5-88
26-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:28:22-85
27    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" /> <!-- Android package visibility setting -->
27-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:29:5-83
27-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:29:22-80
28    <queries>
28-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:35:5-51:15
29
30        <!-- For browser content -->
31        <intent>
31-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:38:9-44:18
32            <action android:name="android.intent.action.VIEW" />
32-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:39:13-65
32-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:39:21-62
33
34            <category android:name="android.intent.category.BROWSABLE" />
34-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:41:13-74
34-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:41:23-71
35
36            <data android:scheme="https" />
36-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:43:13-44
36-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:43:19-41
37        </intent>
38        <!-- End of browser content -->
39        <!-- For CustomTabsService -->
40        <intent>
40-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:47:9-49:18
41            <action android:name="android.support.customtabs.action.CustomTabsService" />
41-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:48:13-90
41-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:48:21-87
42        </intent>
43        <!-- End of CustomTabsService -->
44    </queries>
45
46    <uses-permission android:name="android.permission.WAKE_LOCK" />
46-->[com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\361a6079a625f91adc4631b46632ed13\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:25:5-68
46-->[com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\361a6079a625f91adc4631b46632ed13\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:25:22-65
47    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
47-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
47-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
48
49    <permission
49-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39cfa5352244aa75ffb408322a462e9a\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
50        android:name="com.mahmoudffyt.Gfx_booster.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
50-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39cfa5352244aa75ffb408322a462e9a\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
51        android:protectionLevel="signature" />
51-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39cfa5352244aa75ffb408322a462e9a\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
52
53    <uses-permission android:name="com.mahmoudffyt.Gfx_booster.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
53-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39cfa5352244aa75ffb408322a462e9a\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
53-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39cfa5352244aa75ffb408322a462e9a\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
54
55    <application
55-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:19:5-64:19
56        android:allowBackup="true"
56-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:20:9-35
57        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
57-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39cfa5352244aa75ffb408322a462e9a\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
58        android:dataExtractionRules="@xml/data_extraction_rules"
58-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:21:9-65
59        android:debuggable="true"
60        android:extractNativeLibs="false"
61        android:fullBackupContent="@xml/backup_rules"
61-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:22:9-54
62        android:icon="@mipmap/ic_launcher"
62-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:23:9-43
63        android:label="@string/app_name"
63-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:24:9-41
64        android:roundIcon="@mipmap/ic_launcher_round"
64-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:25:9-54
65        android:supportsRtl="true"
65-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:26:9-35
66        android:testOnly="true"
67        android:theme="@style/Theme.GfxBooster" >
67-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:27:9-48
68        <activity
68-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:30:9-38:20
69            android:name="com.mahmoudffyt.Gfx_booster.SplashActivity"
69-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:31:13-43
70            android:exported="true"
70-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:32:13-36
71            android:theme="@style/Theme.GfxBooster" >
71-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:33:13-52
72            <intent-filter>
72-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:34:13-37:29
73                <action android:name="android.intent.action.MAIN" />
73-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:35:17-69
73-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:35:25-66
74
75                <category android:name="android.intent.category.LAUNCHER" />
75-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:36:17-77
75-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:36:27-74
76            </intent-filter>
77        </activity>
78        <activity
78-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:40:9-43:55
79            android:name="com.mahmoudffyt.Gfx_booster.MainActivity"
79-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:41:13-41
80            android:exported="false"
80-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:42:13-37
81            android:theme="@style/Theme.GfxBooster" />
81-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:43:13-52
82
83        <!-- Tools Activities -->
84        <activity
84-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:46:9-50:58
85            android:name="com.mahmoudffyt.Gfx_booster.tools.DeviceInfoActivity"
85-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:47:13-53
86            android:exported="false"
86-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:48:13-37
87            android:parentActivityName="com.mahmoudffyt.Gfx_booster.MainActivity"
87-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:50:13-55
88            android:theme="@style/Theme.GfxBooster" />
88-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:49:13-52
89        <activity
89-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:52:9-56:58
90            android:name="com.mahmoudffyt.Gfx_booster.tools.PingTestActivity"
90-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:53:13-51
91            android:exported="false"
91-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:54:13-37
92            android:parentActivityName="com.mahmoudffyt.Gfx_booster.MainActivity"
92-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:56:13-55
93            android:theme="@style/Theme.GfxBooster" />
93-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:55:13-52
94        <activity
94-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:58:9-62:58
95            android:name="com.mahmoudffyt.Gfx_booster.tools.ClearCacheActivity"
95-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:59:13-53
96            android:exported="false"
96-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:60:13-37
97            android:parentActivityName="com.mahmoudffyt.Gfx_booster.MainActivity"
97-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:62:13-55
98            android:theme="@style/Theme.GfxBooster" />
98-->C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:61:13-52
99
100        <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
101        <activity
101-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:56:9-61:43
102            android:name="com.google.android.gms.ads.AdActivity"
102-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:57:13-65
103            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
103-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:58:13-122
104            android:exported="false"
104-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:59:13-37
105            android:theme="@android:style/Theme.Translucent" />
105-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:60:13-61
106
107        <provider
107-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:63:9-68:43
108            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
108-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:64:13-76
109            android:authorities="com.mahmoudffyt.Gfx_booster.mobileadsinitprovider"
109-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:65:13-73
110            android:exported="false"
110-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:66:13-37
111            android:initOrder="100" />
111-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:67:13-36
112
113        <service
113-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:70:9-74:43
114            android:name="com.google.android.gms.ads.AdService"
114-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:71:13-64
115            android:enabled="true"
115-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:72:13-35
116            android:exported="false" />
116-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:73:13-37
117
118        <activity
118-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:76:9-80:43
119            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
119-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:77:13-82
120            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
120-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:78:13-122
121            android:exported="false" />
121-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:79:13-37
122        <activity
122-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:81:9-88:43
123            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
123-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:82:13-82
124            android:excludeFromRecents="true"
124-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:83:13-46
125            android:exported="false"
125-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:84:13-37
126            android:launchMode="singleTask"
126-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:85:13-44
127            android:taskAffinity=""
127-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:86:13-36
128            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
128-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:87:13-72
129
130        <property
130-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:90:9-92:62
131            android:name="android.adservices.AD_SERVICES_CONFIG"
131-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:91:13-65
132            android:resource="@xml/gma_ad_services_config" />
132-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:92:13-59
133
134        <activity
134-->[com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f00c7f0a790f3e33848be0f090e5b1aa\transformed\play-services-base-18.0.0\AndroidManifest.xml:20:9-22:45
135            android:name="com.google.android.gms.common.api.GoogleApiActivity"
135-->[com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f00c7f0a790f3e33848be0f090e5b1aa\transformed\play-services-base-18.0.0\AndroidManifest.xml:20:19-85
136            android:exported="false"
136-->[com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f00c7f0a790f3e33848be0f090e5b1aa\transformed\play-services-base-18.0.0\AndroidManifest.xml:22:19-43
137            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
137-->[com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f00c7f0a790f3e33848be0f090e5b1aa\transformed\play-services-base-18.0.0\AndroidManifest.xml:21:19-78
138
139        <meta-data
139-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8aed3e7d451837ba644eaf5cb4530f9\transformed\play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
140            android:name="com.google.android.gms.version"
140-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8aed3e7d451837ba644eaf5cb4530f9\transformed\play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
141            android:value="@integer/google_play_services_version" />
141-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8aed3e7d451837ba644eaf5cb4530f9\transformed\play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
142
143        <uses-library
143-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f87f17dd3d8475a39394a85b8ec91c1c\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
144            android:name="android.ext.adservices"
144-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f87f17dd3d8475a39394a85b8ec91c1c\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
145            android:required="false" />
145-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f87f17dd3d8475a39394a85b8ec91c1c\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
146
147        <provider
147-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35953b689b3bf5f8f4b922cd6bd9f9cc\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
148            android:name="androidx.startup.InitializationProvider"
148-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35953b689b3bf5f8f4b922cd6bd9f9cc\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
149            android:authorities="com.mahmoudffyt.Gfx_booster.androidx-startup"
149-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35953b689b3bf5f8f4b922cd6bd9f9cc\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
150            android:exported="false" >
150-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35953b689b3bf5f8f4b922cd6bd9f9cc\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
151            <meta-data
151-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35953b689b3bf5f8f4b922cd6bd9f9cc\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
152                android:name="androidx.emoji2.text.EmojiCompatInitializer"
152-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35953b689b3bf5f8f4b922cd6bd9f9cc\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
153                android:value="androidx.startup" />
153-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35953b689b3bf5f8f4b922cd6bd9f9cc\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
154            <meta-data
154-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
155                android:name="androidx.work.WorkManagerInitializer"
155-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
156                android:value="androidx.startup" />
156-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
157            <meta-data
157-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77ab6814e4978d3b01e64f4f2c299218\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
158                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
158-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77ab6814e4978d3b01e64f4f2c299218\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
159                android:value="androidx.startup" />
159-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77ab6814e4978d3b01e64f4f2c299218\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
160            <meta-data
160-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
161                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
161-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
162                android:value="androidx.startup" />
162-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
163        </provider>
164
165        <service
165-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
166            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
166-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
167            android:directBootAware="false"
167-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
168            android:enabled="@bool/enable_system_alarm_service_default"
168-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
169            android:exported="false" />
169-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
170        <service
170-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
171            android:name="androidx.work.impl.background.systemjob.SystemJobService"
171-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
172            android:directBootAware="false"
172-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
173            android:enabled="@bool/enable_system_job_service_default"
173-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
174            android:exported="true"
174-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
175            android:permission="android.permission.BIND_JOB_SERVICE" />
175-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
176        <service
176-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
177            android:name="androidx.work.impl.foreground.SystemForegroundService"
177-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
178            android:directBootAware="false"
178-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
179            android:enabled="@bool/enable_system_foreground_service_default"
179-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
180            android:exported="false" />
180-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
181
182        <receiver
182-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
183            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
183-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
184            android:directBootAware="false"
184-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
185            android:enabled="true"
185-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
186            android:exported="false" />
186-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
187        <receiver
187-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
188            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
188-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
189            android:directBootAware="false"
189-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
190            android:enabled="false"
190-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
191            android:exported="false" >
191-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
192            <intent-filter>
192-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
193                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
193-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
193-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
194                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
194-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
194-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
195            </intent-filter>
196        </receiver>
197        <receiver
197-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
198            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
198-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
199            android:directBootAware="false"
199-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
200            android:enabled="false"
200-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
201            android:exported="false" >
201-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
202            <intent-filter>
202-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
203                <action android:name="android.intent.action.BATTERY_OKAY" />
203-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
203-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
204                <action android:name="android.intent.action.BATTERY_LOW" />
204-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
204-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
205            </intent-filter>
206        </receiver>
207        <receiver
207-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
208            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
208-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
209            android:directBootAware="false"
209-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
210            android:enabled="false"
210-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
211            android:exported="false" >
211-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
212            <intent-filter>
212-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
213                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
213-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
213-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
214                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
214-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
214-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
215            </intent-filter>
216        </receiver>
217        <receiver
217-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
218            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
218-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
219            android:directBootAware="false"
219-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
220            android:enabled="false"
220-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
221            android:exported="false" >
221-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
222            <intent-filter>
222-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
223                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
223-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
223-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
224            </intent-filter>
225        </receiver>
226        <receiver
226-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
227            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
227-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
228            android:directBootAware="false"
228-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
229            android:enabled="false"
229-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
230            android:exported="false" >
230-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
231            <intent-filter>
231-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
232                <action android:name="android.intent.action.BOOT_COMPLETED" />
232-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:17-79
232-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:25-76
233                <action android:name="android.intent.action.TIME_SET" />
233-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
233-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
234                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
234-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
234-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
235            </intent-filter>
236        </receiver>
237        <receiver
237-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
238            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
238-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
239            android:directBootAware="false"
239-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
240            android:enabled="@bool/enable_system_alarm_service_default"
240-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
241            android:exported="false" >
241-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
242            <intent-filter>
242-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
243                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
243-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
243-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
244            </intent-filter>
245        </receiver>
246        <receiver
246-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
247            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
247-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
248            android:directBootAware="false"
248-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
249            android:enabled="true"
249-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
250            android:exported="true"
250-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
251            android:permission="android.permission.DUMP" >
251-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
252            <intent-filter>
252-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
253                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
253-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
253-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
254            </intent-filter>
255        </receiver>
256
257        <uses-library
257-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47a842147219744ade4ff5f1952abe41\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
258            android:name="androidx.window.extensions"
258-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47a842147219744ade4ff5f1952abe41\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
259            android:required="false" />
259-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47a842147219744ade4ff5f1952abe41\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
260        <uses-library
260-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47a842147219744ade4ff5f1952abe41\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
261            android:name="androidx.window.sidecar"
261-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47a842147219744ade4ff5f1952abe41\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
262            android:required="false" />
262-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47a842147219744ade4ff5f1952abe41\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
263
264        <receiver
264-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
265            android:name="androidx.profileinstaller.ProfileInstallReceiver"
265-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
266            android:directBootAware="false"
266-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
267            android:enabled="true"
267-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
268            android:exported="true"
268-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
269            android:permission="android.permission.DUMP" >
269-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
270            <intent-filter>
270-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
271                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
271-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
271-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
272            </intent-filter>
273            <intent-filter>
273-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
274                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
274-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
274-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
275            </intent-filter>
276            <intent-filter>
276-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
277                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
277-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
277-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
278            </intent-filter>
279            <intent-filter>
279-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
280                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
280-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
280-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
281            </intent-filter>
282        </receiver>
283
284        <service
284-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ca698fd97820506e2badc883f34104e\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
285            android:name="androidx.room.MultiInstanceInvalidationService"
285-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ca698fd97820506e2badc883f34104e\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
286            android:directBootAware="true"
286-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ca698fd97820506e2badc883f34104e\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
287            android:exported="false" />
287-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ca698fd97820506e2badc883f34104e\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
288    </application>
289
290</manifest>
