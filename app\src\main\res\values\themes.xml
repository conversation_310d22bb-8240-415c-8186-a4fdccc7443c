<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.GfxBooster" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/primary_blue</item>
        <item name="colorPrimaryVariant">@color/primary_blue_dark</item>
        <item name="colorOnPrimary">@color/text_primary</item>

        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/accent_cyan</item>
        <item name="colorSecondaryVariant">@color/accent_cyan_dark</item>
        <item name="colorOnSecondary">@color/text_primary</item>

        <!-- Background colors -->
        <item name="android:colorBackground">@color/background_primary</item>
        <item name="colorSurface">@color/background_card</item>
        <item name="colorOnSurface">@color/text_primary</item>

        <!-- Status bar color. -->
        <item name="android:statusBarColor">@color/background_primary</item>
        <item name="android:windowLightStatusBar">false</item>

        <!-- Navigation bar -->
        <item name="android:navigationBarColor">@color/background_primary</item>

        <!-- Text colors -->
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>

        <!-- Card style -->
        <item name="cardViewStyle">@style/CustomCardView</item>
    </style>

    <!-- Custom Card View Style -->
    <style name="CustomCardView" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/background_card</item>
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">4dp</item>
        <item name="android:layout_margin">8dp</item>
    </style>

    <!-- Custom Button Styles -->
    <style name="PrimaryButton" parent="Widget.MaterialComponents.Button">
        <item name="backgroundTint">@color/primary_blue</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>

    <style name="SecondaryButton" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="strokeColor">@color/accent_cyan</item>
        <item name="android:textColor">@color/accent_cyan</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>

    <style name="ProButton" parent="Widget.MaterialComponents.Button">
        <item name="backgroundTint">@color/pro_gold</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>

    <!-- Text Styles -->
    <style name="HeadlineText">
        <item name="android:textSize">24sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>

    <style name="SubheadText">
        <item name="android:textSize">18sp</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>

    <style name="BodyText">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>

    <style name="CaptionText">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">@color/text_tertiary</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>
</resources>