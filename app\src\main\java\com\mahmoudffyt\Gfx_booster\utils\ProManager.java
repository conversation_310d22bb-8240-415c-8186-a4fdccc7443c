package com.mahmoudffyt.Gfx_booster.utils;

import android.content.Context;
import android.content.SharedPreferences;
import java.util.HashMap;
import java.util.Map;

public class ProManager {
    
    private static final String PREF_NAME = "gfx_booster_pro";
    private static final String KEY_IS_PRO_USER = "is_pro_user";
    private static final String KEY_TEMP_UNLOCK_PREFIX = "temp_unlock_";
    private static final long TEMP_UNLOCK_DURATION = 30 * 60 * 1000; // 30 minutes
    
    private static ProManager instance;
    private SharedPreferences preferences;
    private Map<String, Long> tempUnlockTimes;

    private ProManager(Context context) {
        preferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        tempUnlockTimes = new HashMap<>();
        loadTempUnlockTimes();
    }

    public static synchronized ProManager getInstance(Context context) {
        if (instance == null) {
            instance = new ProManager(context.getApplicationContext());
        }
        return instance;
    }

    // Pro User Management
    public boolean isProUser() {
        return preferences.getBoolean(KEY_IS_PRO_USER, false);
    }

    public void setProUser(boolean isPro) {
        preferences.edit().putBoolean(KEY_IS_PRO_USER, isPro).apply();
    }

    // Temporary Unlock Management
    public void unlockToolTemporarily(String toolName) {
        long unlockTime = System.currentTimeMillis();
        tempUnlockTimes.put(toolName, unlockTime);
        preferences.edit().putLong(KEY_TEMP_UNLOCK_PREFIX + toolName, unlockTime).apply();
    }

    public boolean isToolUnlocked(String toolName) {
        // Pro users have access to everything
        if (isProUser()) {
            return true;
        }

        // Check temporary unlock
        Long unlockTime = tempUnlockTimes.get(toolName);
        if (unlockTime != null) {
            long currentTime = System.currentTimeMillis();
            if (currentTime - unlockTime < TEMP_UNLOCK_DURATION) {
                return true;
            } else {
                // Temporary unlock expired
                removeTempUnlock(toolName);
            }
        }

        return false;
    }

    public long getRemainingUnlockTime(String toolName) {
        Long unlockTime = tempUnlockTimes.get(toolName);
        if (unlockTime != null) {
            long currentTime = System.currentTimeMillis();
            long elapsed = currentTime - unlockTime;
            if (elapsed < TEMP_UNLOCK_DURATION) {
                return TEMP_UNLOCK_DURATION - elapsed;
            }
        }
        return 0;
    }

    private void removeTempUnlock(String toolName) {
        tempUnlockTimes.remove(toolName);
        preferences.edit().remove(KEY_TEMP_UNLOCK_PREFIX + toolName).apply();
    }

    private void loadTempUnlockTimes() {
        Map<String, ?> allPrefs = preferences.getAll();
        for (Map.Entry<String, ?> entry : allPrefs.entrySet()) {
            String key = entry.getKey();
            if (key.startsWith(KEY_TEMP_UNLOCK_PREFIX)) {
                String toolName = key.substring(KEY_TEMP_UNLOCK_PREFIX.length());
                Long unlockTime = (Long) entry.getValue();
                
                // Check if unlock is still valid
                long currentTime = System.currentTimeMillis();
                if (currentTime - unlockTime < TEMP_UNLOCK_DURATION) {
                    tempUnlockTimes.put(toolName, unlockTime);
                } else {
                    // Remove expired unlock
                    preferences.edit().remove(key).apply();
                }
            }
        }
    }

    // Tool Access Check
    public boolean canAccessTool(String toolName) {
        // Define free tools
        String[] freeTools = {
            "Device Info",
            "Game Booster", 
            "Ping Test",
            "Clear Cache",
            "Basic Sensitivity"
        };

        // Check if it's a free tool
        for (String freeTool : freeTools) {
            if (freeTool.equals(toolName)) {
                return true;
            }
        }

        // For pro tools, check unlock status
        return isToolUnlocked(toolName);
    }

    // Usage Tracking
    public void incrementToolUsage(String toolName) {
        String key = "usage_" + toolName;
        int currentUsage = preferences.getInt(key, 0);
        preferences.edit().putInt(key, currentUsage + 1).apply();
    }

    public int getToolUsage(String toolName) {
        return preferences.getInt("usage_" + toolName, 0);
    }

    // Free Trial Management
    public boolean hasFreeTrial(String toolName) {
        String key = "trial_" + toolName;
        return preferences.getInt(key, 0) < 3; // 3 free uses
    }

    public void useFreeTrial(String toolName) {
        String key = "trial_" + toolName;
        int currentTrial = preferences.getInt(key, 0);
        preferences.edit().putInt(key, currentTrial + 1).apply();
    }

    public int getRemainingTrials(String toolName) {
        String key = "trial_" + toolName;
        int used = preferences.getInt(key, 0);
        return Math.max(0, 3 - used);
    }

    // Clear all data (for testing or reset)
    public void clearAllData() {
        preferences.edit().clear().apply();
        tempUnlockTimes.clear();
    }
}
