[{"merged": "com.mahmoudffyt.Gfx_booster.app-debug-55:/drawable_ic_game_controller.xml.flat", "source": "com.mahmoudffyt.Gfx_booster.app-main-57:/drawable/ic_game_controller.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-debug-55:\\mipmap-anydpi_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-main-57:\\mipmap-anydpi\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-debug-55:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-main-57:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-debug-55:\\drawable_ic_home.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-main-57:\\drawable\\ic_home.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-debug-55:\\drawable_pro_label_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-main-57:\\drawable\\pro_label_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-debug-55:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-main-57:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-debug-55:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-main-57:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-debug-55:\\layout_item_tool.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-main-57:\\layout\\item_tool.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-debug-55:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-main-57:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-debug-55:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-main-57:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-debug-55:\\drawable_card_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-main-57:\\drawable\\card_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-debug-55:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-main-57:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-debug-55:\\layout_fragment_home.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-main-57:\\layout\\fragment_home.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-debug-55:\\layout_activity_ping_test.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-main-57:\\layout\\activity_ping_test.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-debug-55:\\mipmap-anydpi_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-main-57:\\mipmap-anydpi\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-debug-55:\\drawable_tip_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-main-57:\\drawable\\tip_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-debug-55:\\drawable_ic_pro_crown.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-main-57:\\drawable\\ic_pro_crown.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-debug-55:\\drawable_gradient_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-main-57:\\drawable\\gradient_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-debug-55:\\layout_activity_device_info.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-main-57:\\layout\\activity_device_info.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-debug-55:\\layout_fragment_tools.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-main-57:\\layout\\fragment_tools.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-debug-55:\\color_bottom_nav_color_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-main-57:\\color\\bottom_nav_color_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-debug-55:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-main-57:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-debug-55:\\layout_card_device_status.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-main-57:\\layout\\card_device_status.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-debug-55:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-main-57:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-debug-55:\\menu_bottom_navigation_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-main-57:\\menu\\bottom_navigation_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-debug-55:\\drawable_ic_tips.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-main-57:\\drawable\\ic_tips.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-debug-55:\\drawable_ic_device_info.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-main-57:\\drawable\\ic_device_info.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-debug-55:\\layout_dialog_loading.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-main-57:\\layout\\dialog_loading.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-debug-55:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-main-57:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-debug-55:\\drawable_pro_card_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-main-57:\\drawable\\pro_card_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-debug-55:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-main-57:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-debug-55:\\layout_activity_clear_cache.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-main-57:\\layout\\activity_clear_cache.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-debug-55:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-main-57:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-debug-55:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-main-57:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-debug-55:\\drawable_ic_tools.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-main-57:\\drawable\\ic_tools.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-debug-55:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-main-57:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-debug-55:\\layout_activity_splash.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-main-57:\\layout\\activity_splash.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-debug-55:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-main-57:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-debug-55:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-main-57:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-debug-55:\\drawable_ic_game_controller.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.mahmoudffyt.Gfx_booster.app-main-57:\\drawable\\ic_game_controller.xml"}]