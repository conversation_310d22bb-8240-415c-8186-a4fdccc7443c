package com.mahmoudffyt.Gfx_booster;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import java.util.Random;

public class SplashActivity extends AppCompatActivity {

    private static final int SPLASH_DURATION = 3000; // 3 seconds
    private TextView tipTextView;
    private String[] loadingTips;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_splash);

        initViews();
        setupLoadingTips();
        showRandomTip();
        
        // Navigate to MainActivity after delay
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            startActivity(new Intent(SplashActivity.this, MainActivity.class));
            finish();
        }, SPLASH_DURATION);
    }

    private void initViews() {
        tipTextView = findViewById(R.id.tip_text);
    }

    private void setupLoadingTips() {
        loadingTips = new String[] {
            getString(R.string.tip_close_apps),
            getString(R.string.tip_restart_device),
            getString(R.string.tip_clear_cache),
            getString(R.string.tip_update_games)
        };
    }

    private void showRandomTip() {
        Random random = new Random();
        int randomIndex = random.nextInt(loadingTips.length);
        tipTextView.setText(loadingTips[randomIndex]);
    }
}
