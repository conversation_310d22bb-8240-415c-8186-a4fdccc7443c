<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Night Mode Theme -->
    <style name="Theme.GfxBooster" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/primary_blue</item>
        <item name="colorPrimaryVariant">@color/primary_blue_dark</item>
        <item name="colorOnPrimary">@color/text_primary</item>

        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/accent_cyan</item>
        <item name="colorSecondaryVariant">@color/accent_cyan_dark</item>
        <item name="colorOnSecondary">@color/text_primary</item>

        <!-- Background colors -->
        <item name="android:colorBackground">@color/background_primary</item>
        <item name="colorSurface">@color/background_card</item>
        <item name="colorOnSurface">@color/text_primary</item>

        <!-- Status bar color. -->
        <item name="android:statusBarColor">@color/background_primary</item>
        <item name="android:windowLightStatusBar">false</item>

        <!-- Navigation bar -->
        <item name="android:navigationBarColor">@color/background_primary</item>

        <!-- Text colors -->
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
    </style>
</resources>