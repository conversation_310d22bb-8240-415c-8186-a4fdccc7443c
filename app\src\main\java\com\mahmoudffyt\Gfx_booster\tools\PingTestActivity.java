package com.mahmoudffyt.Gfx_booster.tools;

import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.widget.Button;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import com.mahmoudffyt.Gfx_booster.R;
import java.io.IOException;
import java.net.InetAddress;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class PingTestActivity extends AppCompatActivity {

    private TextView pingResultText;
    private Button startTestButton;
    private ExecutorService executor;
    private Handler mainHandler;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_ping_test);

        setupToolbar();
        initViews();
        initExecutor();
    }

    private void setupToolbar() {
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("اختبار الاتصال");
        }
    }

    private void initViews() {
        pingResultText = findViewById(R.id.ping_result_text);
        startTestButton = findViewById(R.id.start_test_button);
        
        startTestButton.setOnClickListener(v -> startPingTest());
    }

    private void initExecutor() {
        executor = Executors.newSingleThreadExecutor();
        mainHandler = new Handler(Looper.getMainLooper());
    }

    private void startPingTest() {
        startTestButton.setEnabled(false);
        pingResultText.setText("جاري اختبار الاتصال...");

        executor.execute(() -> {
            String[] servers = {
                "*******",      // Google DNS
                "*******",      // Cloudflare DNS
                "**************" // OpenDNS
            };

            StringBuilder results = new StringBuilder();
            results.append("نتائج اختبار الاتصال:\n\n");

            for (String server : servers) {
                long pingTime = pingServer(server);
                String serverName = getServerName(server);
                
                if (pingTime > 0) {
                    results.append(serverName).append(": ").append(pingTime).append(" ms\n");
                } else {
                    results.append(serverName).append(": فشل الاتصال\n");
                }
            }

            mainHandler.post(() -> {
                pingResultText.setText(results.toString());
                startTestButton.setEnabled(true);
            });
        });
    }

    private long pingServer(String server) {
        try {
            long startTime = System.currentTimeMillis();
            InetAddress address = InetAddress.getByName(server);
            boolean reachable = address.isReachable(5000); // 5 seconds timeout
            long endTime = System.currentTimeMillis();
            
            return reachable ? (endTime - startTime) : -1;
        } catch (IOException e) {
            return -1;
        }
    }

    private String getServerName(String ip) {
        switch (ip) {
            case "*******":
                return "Google DNS";
            case "*******":
                return "Cloudflare DNS";
            case "**************":
                return "OpenDNS";
            default:
                return ip;
        }
    }

    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (executor != null) {
            executor.shutdown();
        }
    }
}
