package com.mahmoudffyt.Gfx_booster.models;

public class ToolItem {
    private String name;
    private int iconResource;
    private boolean isPro;
    private boolean isLocked;

    public ToolItem(String name, int iconResource, boolean isPro) {
        this.name = name;
        this.iconResource = iconResource;
        this.isPro = isPro;
        this.isLocked = isPro; // Pro tools are locked by default
    }

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getIconResource() {
        return iconResource;
    }

    public void setIconResource(int iconResource) {
        this.iconResource = iconResource;
    }

    public boolean isPro() {
        return isPro;
    }

    public void setPro(boolean pro) {
        isPro = pro;
    }

    public boolean isLocked() {
        return isLocked;
    }

    public void setLocked(boolean locked) {
        isLocked = locked;
    }
}
