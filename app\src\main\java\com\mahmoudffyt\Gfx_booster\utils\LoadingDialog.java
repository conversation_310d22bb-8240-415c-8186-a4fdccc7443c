package com.mahmoudffyt.Gfx_booster.utils;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;
import com.mahmoudffyt.Gfx_booster.R;
import java.util.Random;

public class LoadingDialog {
    
    private Dialog dialog;
    private Context context;
    private String[] loadingTips;

    public LoadingDialog(Context context) {
        this.context = context;
        setupDialog();
        setupLoadingTips();
    }

    private void setupDialog() {
        dialog = new Dialog(context);
        View view = LayoutInflater.from(context).inflate(R.layout.dialog_loading, null);
        dialog.setContentView(view);
        dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        dialog.setCancelable(false);
    }

    private void setupLoadingTips() {
        loadingTips = new String[] {
            context.getString(R.string.tip_close_apps),
            context.getString(R.string.tip_restart_device),
            context.getString(R.string.tip_clear_cache),
            context.getString(R.string.tip_update_games)
        };
    }

    public void show() {
        if (dialog != null && !dialog.isShowing()) {
            showRandomTip();
            dialog.show();
        }
    }

    public void dismiss() {
        if (dialog != null && dialog.isShowing()) {
            dialog.dismiss();
        }
    }

    private void showRandomTip() {
        TextView tipTextView = dialog.findViewById(R.id.tip_text);
        if (tipTextView != null) {
            Random random = new Random();
            int randomIndex = random.nextInt(loadingTips.length);
            tipTextView.setText(loadingTips[randomIndex]);
        }
    }

    public boolean isShowing() {
        return dialog != null && dialog.isShowing();
    }
}
