-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:2:1-66:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:2:1-66:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:2:1-66:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:2:1-66:12
MERGED from [androidx.databinding:viewbinding:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d971e572eecbecd0fabb8984a64189cd\transformed\viewbinding-8.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5831f3c8c272e53eee213cf9b04271b8\transformed\navigation-common-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5d7ce7f83d3e84036c82e1cdd6075bd\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb1175afef5eeb2664b6ee594659634f\transformed\navigation-fragment-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-ui:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b5dab633d870da888d03aff26e03c0d\transformed\navigation-ui-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62bd3c5c688c2f010dbf4625ef07a3c9\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18a2741ff67ac4f1320357265089e421\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e44a1d7da30349c596de7b914579445b\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.airbnb.android:lottie:6.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cb2cea0ac5f062a2857bbfaef3c32f3\transformed\lottie-6.2.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5fee654b262bb6b8698685b64713037f\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0463a9d592c97394c719abb3455eba38\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5f1548f2a9744bacde3380ace2d4c8d\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4ddfd669f6a654a175e364685d8e75d\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9601dcafdf0ba1ff9d467198beed2105\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\490597ef4941a643574a61ecb92624ee\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-ads:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23eb2f58d5a6ec699d798dc7cabc37a5\transformed\play-services-ads-22.6.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:17:1-95:12
MERGED from [com.google.android.gms:play-services-ads-base:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc99d190a3878019211163a35ef2224f\transformed\play-services-ads-base-22.6.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.ump:user-messaging-platform:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b4a5edf14ee7b3e609d18a3cdff9e64\transformed\user-messaging-platform-2.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8d4f36f105a93e2afb05e3f7bc5fa0c\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46c6a8eb5a09e7feb60e2d244acea2cf\transformed\play-services-appset-16.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f00c7f0a790f3e33848be0f090e5b1aa\transformed\play-services-base-18.0.0\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2225d4252d30c1b3f9997a1036222aa7\transformed\play-services-tasks-18.0.1\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\361a6079a625f91adc4631b46632ed13\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:17:1-28:12
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8f078bae24215e7913ee4f40e5460ee\transformed\play-services-measurement-base-20.1.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8aed3e7d451837ba644eaf5cb4530f9\transformed\play-services-basement-18.2.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b1f50e89d80cdafc9296dd3b55746d4\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3617b85e511a3708e5d8e75bc6a2b655\transformed\activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4aeb906deb728636f71988e08eb1e28\transformed\activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\516b931df6874b57cebe3c1d548103c4\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6957ab18cf9793a0ad3d44a3b4db50e3\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f87f17dd3d8475a39394a85b8ec91c1c\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd53322057632e46509204805406c011\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35953b689b3bf5f8f4b922cd6bd9f9cc\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5a43ce795ea3b6580a9b55ac03e8e5e\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8577f828bbf280a2b659dbf4284beabc\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddcf4884e7533695e04eaecaafee2222\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a26f2880ad91263181658f7b3ee29693\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed03c5b125ac7ecf2812495c819e7afc\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77ab6814e4978d3b01e64f4f2c299218\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db3f324c23a2846f0686516c7f902fd0\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\400ef67ef9f93e40eacfe9fcb5faaf68\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d8c63231ddf6fc35a3fd262ef3d9088\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6237733b34ca74595e07c25c7179d0fb\transformed\core-ktx-1.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5be30d4eb3f81e651d80b0d4c47b910b\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\03451b1e242630e1d9f61a8123743418\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06cd904333500753086050b08cf2204b\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4cc626a1e4523c990467127296881022\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6be6269e3003bafd9a79254b1027262\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e070d170ac6544f436cb618d1e2660e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e8f3d8ca4e290fc4f994f79c9c9095c\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e8034aa775e438e75827a92eca37ee7f\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44f5b490272c4ef3dc5bfd1aa9981ba2\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51ed4ac0620ff88f6e9f99d091f6bd6\transformed\browser-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a47e309173808104b3f330bb205712f3\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb436f3f01843cdb534350cb9422f5fe\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47a842147219744ade4ff5f1952abe41\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39cfa5352244aa75ffb408322a462e9a\transformed\core-1.13.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e42a8b8527b074991bfb4164731dd5e\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6be3858bb6c67c85eddb1a0fba51f82\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad51ee0875ad87ccb597339fbf646f01\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e6e516211e5f2cd59372a0fe3ef6ccd\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ca698fd97820506e2badc883f34104e\transformed\room-runtime-2.2.5\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52736b96523a4d0b5f29aa4940e2ba77\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f91015ba70e4f464550cdc772a58120e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d208cea9ed5b7fa6b967dc61fda1a09\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16543da08d18e0235960147f802d115\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3289a7561ab314d5d02f587b4a53fa77\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5787f2498905f84343671438dd34ad24\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ade528901f3505e76902f64e91f29ea\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3c703e2b413d7be0f9599122c89136e\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69b87eddaa0a0e74ff66b7881c161388\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06d2fcafbff947405cf68acd0414871e\transformed\sqlite-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8abd74c4cf2f6cf712fafcf29141bf21\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:6:5-67
MERGED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\361a6079a625f91adc4631b46632ed13\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\361a6079a625f91adc4631b46632ed13\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:23:5-67
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\361a6079a625f91adc4631b46632ed13\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\361a6079a625f91adc4631b46632ed13\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:26:5-79
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:26:5-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:8:5-76
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:8:22-73
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:9:5-78
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:9:22-75
uses-permission#android.permission.GET_TASKS
ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:10:5-68
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:10:22-65
uses-permission#android.permission.PACKAGE_USAGE_STATS
ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:11:5-12:47
	tools:ignore
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:12:9-44
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:11:22-75
meta-data#com.google.android.gms.ads.APPLICATION_ID
ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:15:5-17:65
	android:value
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:17:9-63
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:16:9-65
application
ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:19:5-64:19
INJECTED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:19:5-64:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62bd3c5c688c2f010dbf4625ef07a3c9\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62bd3c5c688c2f010dbf4625ef07a3c9\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18a2741ff67ac4f1320357265089e421\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18a2741ff67ac4f1320357265089e421\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [com.airbnb.android:lottie:6.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cb2cea0ac5f062a2857bbfaef3c32f3\transformed\lottie-6.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:lottie:6.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cb2cea0ac5f062a2857bbfaef3c32f3\transformed\lottie-6.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:53:5-93:19
MERGED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:53:5-93:19
MERGED from [com.google.android.gms:play-services-ads-base:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc99d190a3878019211163a35ef2224f\transformed\play-services-ads-base-22.6.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-ads-base:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc99d190a3878019211163a35ef2224f\transformed\play-services-ads-base-22.6.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8d4f36f105a93e2afb05e3f7bc5fa0c\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8d4f36f105a93e2afb05e3f7bc5fa0c\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46c6a8eb5a09e7feb60e2d244acea2cf\transformed\play-services-appset-16.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46c6a8eb5a09e7feb60e2d244acea2cf\transformed\play-services-appset-16.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f00c7f0a790f3e33848be0f090e5b1aa\transformed\play-services-base-18.0.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f00c7f0a790f3e33848be0f090e5b1aa\transformed\play-services-base-18.0.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2225d4252d30c1b3f9997a1036222aa7\transformed\play-services-tasks-18.0.1\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2225d4252d30c1b3f9997a1036222aa7\transformed\play-services-tasks-18.0.1\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8f078bae24215e7913ee4f40e5460ee\transformed\play-services-measurement-base-20.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8f078bae24215e7913ee4f40e5460ee\transformed\play-services-measurement-base-20.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8aed3e7d451837ba644eaf5cb4530f9\transformed\play-services-basement-18.2.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8aed3e7d451837ba644eaf5cb4530f9\transformed\play-services-basement-18.2.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f87f17dd3d8475a39394a85b8ec91c1c\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f87f17dd3d8475a39394a85b8ec91c1c\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35953b689b3bf5f8f4b922cd6bd9f9cc\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35953b689b3bf5f8f4b922cd6bd9f9cc\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77ab6814e4978d3b01e64f4f2c299218\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77ab6814e4978d3b01e64f4f2c299218\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47a842147219744ade4ff5f1952abe41\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47a842147219744ade4ff5f1952abe41\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39cfa5352244aa75ffb408322a462e9a\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39cfa5352244aa75ffb408322a462e9a\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ca698fd97820506e2badc883f34104e\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ca698fd97820506e2badc883f34104e\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f91015ba70e4f464550cdc772a58120e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f91015ba70e4f464550cdc772a58120e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16543da08d18e0235960147f802d115\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16543da08d18e0235960147f802d115\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39cfa5352244aa75ffb408322a462e9a\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:26:9-35
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:24:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:22:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:25:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:28:9-29
	android:icon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:23:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:20:9-35
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:27:9-48
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:21:9-65
activity#com.mahmoudffyt.Gfx_booster.SplashActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:30:9-38:20
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:32:13-36
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:33:13-52
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:31:13-43
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:34:13-37:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:35:17-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:35:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:36:17-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:36:27-74
activity#com.mahmoudffyt.Gfx_booster.MainActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:40:9-43:55
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:42:13-37
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:43:13-52
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:41:13-41
activity#com.mahmoudffyt.Gfx_booster.tools.DeviceInfoActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:46:9-50:58
	android:parentActivityName
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:50:13-55
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:48:13-37
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:49:13-52
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:47:13-53
activity#com.mahmoudffyt.Gfx_booster.tools.PingTestActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:52:9-56:58
	android:parentActivityName
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:56:13-55
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:54:13-37
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:55:13-52
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:53:13-51
activity#com.mahmoudffyt.Gfx_booster.tools.ClearCacheActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:58:9-62:58
	android:parentActivityName
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:62:13-55
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:60:13-37
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:61:13-52
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml:59:13-53
uses-sdk
INJECTED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d971e572eecbecd0fabb8984a64189cd\transformed\viewbinding-8.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d971e572eecbecd0fabb8984a64189cd\transformed\viewbinding-8.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5831f3c8c272e53eee213cf9b04271b8\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5831f3c8c272e53eee213cf9b04271b8\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5d7ce7f83d3e84036c82e1cdd6075bd\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5d7ce7f83d3e84036c82e1cdd6075bd\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb1175afef5eeb2664b6ee594659634f\transformed\navigation-fragment-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb1175afef5eeb2664b6ee594659634f\transformed\navigation-fragment-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b5dab633d870da888d03aff26e03c0d\transformed\navigation-ui-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b5dab633d870da888d03aff26e03c0d\transformed\navigation-ui-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62bd3c5c688c2f010dbf4625ef07a3c9\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62bd3c5c688c2f010dbf4625ef07a3c9\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18a2741ff67ac4f1320357265089e421\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18a2741ff67ac4f1320357265089e421\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e44a1d7da30349c596de7b914579445b\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e44a1d7da30349c596de7b914579445b\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.airbnb.android:lottie:6.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cb2cea0ac5f062a2857bbfaef3c32f3\transformed\lottie-6.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cb2cea0ac5f062a2857bbfaef3c32f3\transformed\lottie-6.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5fee654b262bb6b8698685b64713037f\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5fee654b262bb6b8698685b64713037f\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0463a9d592c97394c719abb3455eba38\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0463a9d592c97394c719abb3455eba38\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5f1548f2a9744bacde3380ace2d4c8d\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5f1548f2a9744bacde3380ace2d4c8d\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4ddfd669f6a654a175e364685d8e75d\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4ddfd669f6a654a175e364685d8e75d\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9601dcafdf0ba1ff9d467198beed2105\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9601dcafdf0ba1ff9d467198beed2105\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\490597ef4941a643574a61ecb92624ee\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\490597ef4941a643574a61ecb92624ee\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23eb2f58d5a6ec699d798dc7cabc37a5\transformed\play-services-ads-22.6.0\AndroidManifest.xml:21:5-23:52
MERGED from [com.google.android.gms:play-services-ads:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23eb2f58d5a6ec699d798dc7cabc37a5\transformed\play-services-ads-22.6.0\AndroidManifest.xml:21:5-23:52
MERGED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-ads-base:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc99d190a3878019211163a35ef2224f\transformed\play-services-ads-base-22.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads-base:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc99d190a3878019211163a35ef2224f\transformed\play-services-ads-base-22.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.ump:user-messaging-platform:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b4a5edf14ee7b3e609d18a3cdff9e64\transformed\user-messaging-platform-2.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.ump:user-messaging-platform:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b4a5edf14ee7b3e609d18a3cdff9e64\transformed\user-messaging-platform-2.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8d4f36f105a93e2afb05e3f7bc5fa0c\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8d4f36f105a93e2afb05e3f7bc5fa0c\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46c6a8eb5a09e7feb60e2d244acea2cf\transformed\play-services-appset-16.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46c6a8eb5a09e7feb60e2d244acea2cf\transformed\play-services-appset-16.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f00c7f0a790f3e33848be0f090e5b1aa\transformed\play-services-base-18.0.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f00c7f0a790f3e33848be0f090e5b1aa\transformed\play-services-base-18.0.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2225d4252d30c1b3f9997a1036222aa7\transformed\play-services-tasks-18.0.1\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2225d4252d30c1b3f9997a1036222aa7\transformed\play-services-tasks-18.0.1\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\361a6079a625f91adc4631b46632ed13\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\361a6079a625f91adc4631b46632ed13\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8f078bae24215e7913ee4f40e5460ee\transformed\play-services-measurement-base-20.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8f078bae24215e7913ee4f40e5460ee\transformed\play-services-measurement-base-20.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8aed3e7d451837ba644eaf5cb4530f9\transformed\play-services-basement-18.2.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8aed3e7d451837ba644eaf5cb4530f9\transformed\play-services-basement-18.2.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b1f50e89d80cdafc9296dd3b55746d4\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b1f50e89d80cdafc9296dd3b55746d4\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3617b85e511a3708e5d8e75bc6a2b655\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3617b85e511a3708e5d8e75bc6a2b655\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4aeb906deb728636f71988e08eb1e28\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4aeb906deb728636f71988e08eb1e28\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\516b931df6874b57cebe3c1d548103c4\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\516b931df6874b57cebe3c1d548103c4\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6957ab18cf9793a0ad3d44a3b4db50e3\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6957ab18cf9793a0ad3d44a3b4db50e3\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f87f17dd3d8475a39394a85b8ec91c1c\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f87f17dd3d8475a39394a85b8ec91c1c\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd53322057632e46509204805406c011\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd53322057632e46509204805406c011\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35953b689b3bf5f8f4b922cd6bd9f9cc\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35953b689b3bf5f8f4b922cd6bd9f9cc\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5a43ce795ea3b6580a9b55ac03e8e5e\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5a43ce795ea3b6580a9b55ac03e8e5e\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8577f828bbf280a2b659dbf4284beabc\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8577f828bbf280a2b659dbf4284beabc\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddcf4884e7533695e04eaecaafee2222\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ddcf4884e7533695e04eaecaafee2222\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a26f2880ad91263181658f7b3ee29693\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a26f2880ad91263181658f7b3ee29693\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed03c5b125ac7ecf2812495c819e7afc\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed03c5b125ac7ecf2812495c819e7afc\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77ab6814e4978d3b01e64f4f2c299218\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77ab6814e4978d3b01e64f4f2c299218\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db3f324c23a2846f0686516c7f902fd0\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db3f324c23a2846f0686516c7f902fd0\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\400ef67ef9f93e40eacfe9fcb5faaf68\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\400ef67ef9f93e40eacfe9fcb5faaf68\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d8c63231ddf6fc35a3fd262ef3d9088\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d8c63231ddf6fc35a3fd262ef3d9088\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6237733b34ca74595e07c25c7179d0fb\transformed\core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6237733b34ca74595e07c25c7179d0fb\transformed\core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5be30d4eb3f81e651d80b0d4c47b910b\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5be30d4eb3f81e651d80b0d4c47b910b\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\03451b1e242630e1d9f61a8123743418\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\03451b1e242630e1d9f61a8123743418\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06cd904333500753086050b08cf2204b\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06cd904333500753086050b08cf2204b\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4cc626a1e4523c990467127296881022\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4cc626a1e4523c990467127296881022\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6be6269e3003bafd9a79254b1027262\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6be6269e3003bafd9a79254b1027262\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e070d170ac6544f436cb618d1e2660e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e070d170ac6544f436cb618d1e2660e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e8f3d8ca4e290fc4f994f79c9c9095c\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e8f3d8ca4e290fc4f994f79c9c9095c\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e8034aa775e438e75827a92eca37ee7f\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e8034aa775e438e75827a92eca37ee7f\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44f5b490272c4ef3dc5bfd1aa9981ba2\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44f5b490272c4ef3dc5bfd1aa9981ba2\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51ed4ac0620ff88f6e9f99d091f6bd6\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51ed4ac0620ff88f6e9f99d091f6bd6\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a47e309173808104b3f330bb205712f3\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a47e309173808104b3f330bb205712f3\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb436f3f01843cdb534350cb9422f5fe\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb436f3f01843cdb534350cb9422f5fe\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47a842147219744ade4ff5f1952abe41\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47a842147219744ade4ff5f1952abe41\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39cfa5352244aa75ffb408322a462e9a\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39cfa5352244aa75ffb408322a462e9a\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e42a8b8527b074991bfb4164731dd5e\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e42a8b8527b074991bfb4164731dd5e\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6be3858bb6c67c85eddb1a0fba51f82\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6be3858bb6c67c85eddb1a0fba51f82\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad51ee0875ad87ccb597339fbf646f01\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad51ee0875ad87ccb597339fbf646f01\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e6e516211e5f2cd59372a0fe3ef6ccd\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e6e516211e5f2cd59372a0fe3ef6ccd\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ca698fd97820506e2badc883f34104e\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ca698fd97820506e2badc883f34104e\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52736b96523a4d0b5f29aa4940e2ba77\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52736b96523a4d0b5f29aa4940e2ba77\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f91015ba70e4f464550cdc772a58120e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f91015ba70e4f464550cdc772a58120e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d208cea9ed5b7fa6b967dc61fda1a09\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d208cea9ed5b7fa6b967dc61fda1a09\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16543da08d18e0235960147f802d115\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c16543da08d18e0235960147f802d115\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3289a7561ab314d5d02f587b4a53fa77\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3289a7561ab314d5d02f587b4a53fa77\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5787f2498905f84343671438dd34ad24\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5787f2498905f84343671438dd34ad24\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ade528901f3505e76902f64e91f29ea\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ade528901f3505e76902f64e91f29ea\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3c703e2b413d7be0f9599122c89136e\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3c703e2b413d7be0f9599122c89136e\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69b87eddaa0a0e74ff66b7881c161388\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69b87eddaa0a0e74ff66b7881c161388\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06d2fcafbff947405cf68acd0414871e\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06d2fcafbff947405cf68acd0414871e\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8abd74c4cf2f6cf712fafcf29141bf21\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8abd74c4cf2f6cf712fafcf29141bf21\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
	tools:overrideLibrary
		ADDED from [com.google.android.gms:play-services-ads:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\23eb2f58d5a6ec699d798dc7cabc37a5\transformed\play-services-ads-22.6.0\AndroidManifest.xml:23:9-49
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\AndroidManifest.xml
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8d4f36f105a93e2afb05e3f7bc5fa0c\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8d4f36f105a93e2afb05e3f7bc5fa0c\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\361a6079a625f91adc4631b46632ed13\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\361a6079a625f91adc4631b46632ed13\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:26:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:27:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:27:22-79
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:28:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:28:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_TOPICS
ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:29:5-83
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:29:22-80
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:30:5-32:31
REJECTED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:27:5-81
	tools:node
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:32:9-28
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:31:9-65
queries
ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:35:5-51:15
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:38:9-44:18
action#android.intent.action.VIEW
ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:39:13-65
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:39:21-62
category#android.intent.category.BROWSABLE
ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:41:13-74
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:41:23-71
data
ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:43:13-44
	android:scheme
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:43:19-41
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:47:9-49:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:48:13-90
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:48:21-87
activity#com.google.android.gms.ads.AdActivity
ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:56:9-61:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:59:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:61:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:58:13-122
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:60:13-61
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:57:13-65
provider#com.google.android.gms.ads.MobileAdsInitProvider
ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:63:9-68:43
	android:authorities
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:65:13-73
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:66:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:68:13-40
	android:initOrder
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:67:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:64:13-76
service#com.google.android.gms.ads.AdService
ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:70:9-74:43
	android:enabled
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:72:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:73:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:74:13-40
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:71:13-64
activity#com.google.android.gms.ads.OutOfContextTestingActivity
ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:76:9-80:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:79:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:80:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:78:13-122
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:77:13-82
activity#com.google.android.gms.ads.NotificationHandlerActivity
ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:81:9-88:43
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:83:13-46
	android:launchMode
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:85:13-44
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:84:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:88:13-40
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:87:13-72
	android:taskAffinity
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:86:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:82:13-82
property#android.adservices.AD_SERVICES_CONFIG
ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:90:9-92:62
	android:resource
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:92:13-59
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59fb950e7d4c96170c23f8e43317c790\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:91:13-65
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f00c7f0a790f3e33848be0f090e5b1aa\transformed\play-services-base-18.0.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f00c7f0a790f3e33848be0f090e5b1aa\transformed\play-services-base-18.0.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f00c7f0a790f3e33848be0f090e5b1aa\transformed\play-services-base-18.0.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f00c7f0a790f3e33848be0f090e5b1aa\transformed\play-services-base-18.0.0\AndroidManifest.xml:20:19-85
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\361a6079a625f91adc4631b46632ed13\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:5-68
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\361a6079a625f91adc4631b46632ed13\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:25:22-65
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8aed3e7d451837ba644eaf5cb4530f9\transformed\play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8aed3e7d451837ba644eaf5cb4530f9\transformed\play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8aed3e7d451837ba644eaf5cb4530f9\transformed\play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f87f17dd3d8475a39394a85b8ec91c1c\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f87f17dd3d8475a39394a85b8ec91c1c\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f87f17dd3d8475a39394a85b8ec91c1c\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35953b689b3bf5f8f4b922cd6bd9f9cc\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77ab6814e4978d3b01e64f4f2c299218\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77ab6814e4978d3b01e64f4f2c299218\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f91015ba70e4f464550cdc772a58120e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f91015ba70e4f464550cdc772a58120e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35953b689b3bf5f8f4b922cd6bd9f9cc\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35953b689b3bf5f8f4b922cd6bd9f9cc\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35953b689b3bf5f8f4b922cd6bd9f9cc\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35953b689b3bf5f8f4b922cd6bd9f9cc\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35953b689b3bf5f8f4b922cd6bd9f9cc\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35953b689b3bf5f8f4b922cd6bd9f9cc\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35953b689b3bf5f8f4b922cd6bd9f9cc\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
	android:value
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:46:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:53:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcc4e568134ca2254538b0956cc48374\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77ab6814e4978d3b01e64f4f2c299218\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77ab6814e4978d3b01e64f4f2c299218\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77ab6814e4978d3b01e64f4f2c299218\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47a842147219744ade4ff5f1952abe41\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47a842147219744ade4ff5f1952abe41\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47a842147219744ade4ff5f1952abe41\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47a842147219744ade4ff5f1952abe41\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47a842147219744ade4ff5f1952abe41\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47a842147219744ade4ff5f1952abe41\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39cfa5352244aa75ffb408322a462e9a\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39cfa5352244aa75ffb408322a462e9a\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39cfa5352244aa75ffb408322a462e9a\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
permission#com.mahmoudffyt.Gfx_booster.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39cfa5352244aa75ffb408322a462e9a\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39cfa5352244aa75ffb408322a462e9a\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39cfa5352244aa75ffb408322a462e9a\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39cfa5352244aa75ffb408322a462e9a\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39cfa5352244aa75ffb408322a462e9a\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
uses-permission#com.mahmoudffyt.Gfx_booster.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39cfa5352244aa75ffb408322a462e9a\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39cfa5352244aa75ffb408322a462e9a\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6633e5623e2f7c0de9b9508de28b2200\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ca698fd97820506e2badc883f34104e\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
	android:exported
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ca698fd97820506e2badc883f34104e\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ca698fd97820506e2badc883f34104e\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ca698fd97820506e2badc883f34104e\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
