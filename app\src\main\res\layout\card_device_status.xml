<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    style="@style/CustomCardView"
    android:layout_margin="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp"
        android:gravity="center">

        <!-- Status Icon -->
        <ImageView
            android:id="@+id/status_icon"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/ic_device_info"
            android:layout_marginBottom="8dp"
            app:tint="@color/accent_cyan" />

        <!-- Status Value -->
        <TextView
            android:id="@+id/status_value"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="85%"
            android:textSize="16sp"
            android:textColor="@color/text_primary"
            android:fontFamily="sans-serif-medium"
            android:gravity="center"
            android:layout_marginBottom="4dp" />

        <!-- Status Label -->
        <TextView
            android:id="@+id/status_label"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/battery"
            style="@style/CaptionText"
            android:gravity="center" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
