package com.mahmoudffyt.Gfx_booster;

import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.mahmoudffyt.Gfx_booster.ads.AdManager;
import com.mahmoudffyt.Gfx_booster.fragments.HomeFragment;
import com.mahmoudffyt.Gfx_booster.fragments.ToolsFragment;
import com.mahmoudffyt.Gfx_booster.utils.LoadingDialog;

public class MainActivity extends AppCompatActivity {

    private BottomNavigationView bottomNavigationView;
    private FragmentManager fragmentManager;
    private AdManager adManager;
    private LoadingDialog loadingDialog;
    private long lastFragmentChangeTime = 0;
    private static final long FRAGMENT_CHANGE_COOLDOWN = 5 * 60 * 1000; // 5 minutes

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        initViews();
        initAds();
        setupBottomNavigation();

        // Load default fragment (Home)
        if (savedInstanceState == null) {
            loadFragment(new HomeFragment());
        }
    }

    private void initAds() {
        adManager = AdManager.getInstance(this);
        loadingDialog = new LoadingDialog(this);
    }

    private void initViews() {
        bottomNavigationView = findViewById(R.id.bottom_navigation);
        fragmentManager = getSupportFragmentManager();
    }

    private void setupBottomNavigation() {
        bottomNavigationView.setOnItemSelectedListener(item -> {
            Fragment selectedFragment = null;

            if (item.getItemId() == R.id.nav_home) {
                selectedFragment = new HomeFragment();
            } else if (item.getItemId() == R.id.nav_tools) {
                selectedFragment = new ToolsFragment();
            }

            if (selectedFragment != null) {
                loadFragmentWithAd(selectedFragment);
                return true;
            }
            return false;
        });
    }

    private void loadFragmentWithAd(Fragment fragment) {
        long currentTime = System.currentTimeMillis();

        // Show interstitial ad if cooldown period has passed
        if (currentTime - lastFragmentChangeTime >= FRAGMENT_CHANGE_COOLDOWN) {
            lastFragmentChangeTime = currentTime;

            if (adManager.isInterstitialReady()) {
                adManager.showInterstitialAd(this, new AdManager.InterstitialAdCallback() {
                    @Override
                    public void onAdClosed() {
                        loadFragment(fragment);
                    }

                    @Override
                    public void onAdFailed() {
                        loadFragment(fragment);
                    }
                });
                return;
            }
        }

        // Load fragment directly if no ad or cooldown active
        loadFragment(fragment);
    }

    private void loadFragment(Fragment fragment) {
        // Show loading dialog
        loadingDialog.show();

        // Simulate loading time
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            FragmentTransaction transaction = fragmentManager.beginTransaction();
            transaction.replace(R.id.fragment_container, fragment);
            transaction.commit();

            // Hide loading dialog
            loadingDialog.dismiss();
        }, 1000); // 1 second loading time
    }
}
