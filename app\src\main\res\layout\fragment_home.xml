<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/app_name"
            android:textSize="28sp"
            android:textColor="@color/text_primary"
            android:fontFamily="sans-serif-medium"
            android:gravity="center"
            android:layout_marginBottom="24dp" />

        <!-- Device Status Section -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/device_status"
            style="@style/HeadlineText"
            android:layout_marginBottom="12dp" />

        <!-- Device Status Cards Grid -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:weightSum="4"
            android:layout_marginBottom="24dp">

            <!-- Battery Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="4dp"
                style="@style/CustomCardView">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="12dp"
                    android:gravity="center">

                    <ImageView
                        android:id="@+id/battery_icon"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:src="@drawable/ic_device_info"
                        android:layout_marginBottom="8dp"
                        app:tint="@color/battery_good" />

                    <TextView
                        android:id="@+id/battery_value"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="85%"
                        android:textSize="16sp"
                        android:textColor="@color/text_primary"
                        android:fontFamily="sans-serif-medium"
                        android:gravity="center"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/battery"
                        style="@style/CaptionText"
                        android:gravity="center" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- RAM Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="4dp"
                style="@style/CustomCardView">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="12dp"
                    android:gravity="center">

                    <ImageView
                        android:id="@+id/ram_icon"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:src="@drawable/ic_device_info"
                        android:layout_marginBottom="8dp"
                        app:tint="@color/ram_good" />

                    <TextView
                        android:id="@+id/ram_value"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="4.2GB"
                        android:textSize="16sp"
                        android:textColor="@color/text_primary"
                        android:fontFamily="sans-serif-medium"
                        android:gravity="center"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/ram"
                        style="@style/CaptionText"
                        android:gravity="center" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- CPU Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="4dp"
                style="@style/CustomCardView">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="12dp"
                    android:gravity="center">

                    <ImageView
                        android:id="@+id/cpu_icon"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:src="@drawable/ic_device_info"
                        android:layout_marginBottom="8dp"
                        app:tint="@color/cpu_normal" />

                    <TextView
                        android:id="@+id/cpu_value"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="25%"
                        android:textSize="16sp"
                        android:textColor="@color/text_primary"
                        android:fontFamily="sans-serif-medium"
                        android:gravity="center"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/cpu"
                        style="@style/CaptionText"
                        android:gravity="center" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Network Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="4dp"
                style="@style/CustomCardView">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="12dp"
                    android:gravity="center">

                    <ImageView
                        android:id="@+id/network_icon"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:src="@drawable/ic_device_info"
                        android:layout_marginBottom="8dp"
                        app:tint="@color/accent_cyan" />

                    <TextView
                        android:id="@+id/network_value"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="WiFi"
                        android:textSize="16sp"
                        android:textColor="@color/text_primary"
                        android:fontFamily="sans-serif-medium"
                        android:gravity="center"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/network"
                        style="@style/CaptionText"
                        android:gravity="center" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

        <!-- Device Info Section -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/device_info"
            style="@style/HeadlineText"
            android:layout_marginBottom="12dp" />

        <!-- Device Info Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            style="@style/CustomCardView"
            android:layout_marginBottom="24dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <ImageView
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@drawable/ic_device_info"
                    android:layout_gravity="center"
                    android:layout_marginBottom="12dp"
                    app:tint="@color/accent_cyan" />

                <TextView
                    android:id="@+id/device_model"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Device Model"
                    style="@style/SubheadText"
                    android:gravity="center"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:id="@+id/android_version"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Android Version"
                    style="@style/BodyText"
                    android:gravity="center"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:id="@+id/total_ram"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Total RAM"
                    style="@style/BodyText"
                    android:gravity="center" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Game Booster Section -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/game_booster"
            style="@style/HeadlineText"
            android:layout_marginBottom="12dp" />

        <!-- Game Booster Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            style="@style/CustomCardView"
            android:layout_marginBottom="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <ImageView
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@drawable/ic_game_controller"
                    android:layout_gravity="center"
                    android:layout_marginBottom="12dp"
                    app:tint="@color/accent_cyan" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/add_games"
                    style="@style/SubheadText"
                    android:gravity="center"
                    android:layout_marginBottom="16dp" />

                <!-- Games RecyclerView -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/games_recycler"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp" />

                <!-- Add Game Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/add_game_button"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/add_games"
                    style="@style/PrimaryButton" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Banner Ad Placeholder -->
        <FrameLayout
            android:id="@+id/banner_ad_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:background="@color/background_secondary"
            android:minHeight="50dp" />

    </LinearLayout>

</ScrollView>
