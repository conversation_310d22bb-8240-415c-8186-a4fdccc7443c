package com.mahmoudffyt.Gfx_booster.fragments;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.mahmoudffyt.Gfx_booster.R;
import com.mahmoudffyt.Gfx_booster.adapters.ToolsAdapter;
import com.mahmoudffyt.Gfx_booster.ads.AdManager;
import com.mahmoudffyt.Gfx_booster.models.ToolItem;
import com.mahmoudffyt.Gfx_booster.tools.DeviceInfoActivity;
import com.mahmoudffyt.Gfx_booster.tools.PingTestActivity;
import com.mahmoudffyt.Gfx_booster.tools.ClearCacheActivity;
import com.mahmoudffyt.Gfx_booster.utils.ProManager;
import java.util.ArrayList;
import java.util.List;

public class ToolsFragment extends Fragment implements ToolsAdapter.OnToolClickListener {

    private RecyclerView freeToolsRecyclerView;
    private RecyclerView proToolsRecyclerView;
    private ToolsAdapter freeToolsAdapter;
    private ToolsAdapter proToolsAdapter;
    private AdManager adManager;
    private ProManager proManager;
    private FrameLayout bannerAdContainer;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_tools, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // Initialize managers
        adManager = AdManager.getInstance(requireContext());
        proManager = ProManager.getInstance(requireContext());

        initViews(view);
        setupRecyclerViews();
        loadTools();
        setupBannerAd();
    }

    private void initViews(View view) {
        freeToolsRecyclerView = view.findViewById(R.id.free_tools_recycler);
        proToolsRecyclerView = view.findViewById(R.id.pro_tools_recycler);
        bannerAdContainer = view.findViewById(R.id.banner_ad_container);
    }

    private void setupRecyclerViews() {
        // Setup free tools RecyclerView
        freeToolsRecyclerView.setLayoutManager(new GridLayoutManager(getContext(), 2));
        freeToolsAdapter = new ToolsAdapter(getFreeTools(), false);
        freeToolsAdapter.setOnToolClickListener(this);
        freeToolsRecyclerView.setAdapter(freeToolsAdapter);

        // Setup pro tools RecyclerView
        proToolsRecyclerView.setLayoutManager(new GridLayoutManager(getContext(), 2));
        proToolsAdapter = new ToolsAdapter(getProTools(), true);
        proToolsAdapter.setOnToolClickListener(this);
        proToolsRecyclerView.setAdapter(proToolsAdapter);
    }

    private void setupBannerAd() {
        if (bannerAdContainer != null) {
            adManager.loadBannerAd(bannerAdContainer);
        }
    }

    private void loadTools() {
        // Load tools data
    }

    private List<ToolItem> getFreeTools() {
        List<ToolItem> freeTools = new ArrayList<>();
        freeTools.add(new ToolItem("Device Info", R.drawable.ic_device_info, false));
        freeTools.add(new ToolItem("Game Booster", R.drawable.ic_game_controller, false));
        freeTools.add(new ToolItem("Ping Test", R.drawable.ic_tools, false));
        freeTools.add(new ToolItem("Clear Cache", R.drawable.ic_tools, false));
        freeTools.add(new ToolItem("Basic Sensitivity", R.drawable.ic_tools, false));
        return freeTools;
    }

    private List<ToolItem> getProTools() {
        List<ToolItem> proTools = new ArrayList<>();
        proTools.add(new ToolItem("Smart Aim", R.drawable.ic_pro_crown, true));
        proTools.add(new ToolItem("Crosshair Tool", R.drawable.ic_pro_crown, true));
        proTools.add(new ToolItem("FPS Counter", R.drawable.ic_pro_crown, true));
        proTools.add(new ToolItem("Advanced Sensitivity", R.drawable.ic_pro_crown, true));
        proTools.add(new ToolItem("Graphics Control", R.drawable.ic_pro_crown, true));
        proTools.add(new ToolItem("Network Optimizer", R.drawable.ic_pro_crown, true));
        return proTools;
    }

    @Override
    public void onToolClick(ToolItem tool) {
        String toolName = tool.getName();

        // Check if user can access this tool
        if (proManager.canAccessTool(toolName)) {
            // User has access, launch tool
            launchTool(tool);
            proManager.incrementToolUsage(toolName);
        } else if (tool.isPro()) {
            // Pro tool - check if user has free trial or show rewarded ad
            if (proManager.hasFreeTrial(toolName)) {
                showTrialDialog(tool);
            } else {
                showRewardedAdForTool(tool);
            }
        } else {
            // This shouldn't happen for free tools
            launchTool(tool);
        }
    }

    private void showTrialDialog(ToolItem tool) {
        String toolName = tool.getName();
        int remainingTrials = proManager.getRemainingTrials(toolName);

        new androidx.appcompat.app.AlertDialog.Builder(requireContext())
            .setTitle("تجربة مجانية")
            .setMessage("لديك " + remainingTrials + " استخدامات مجانية متبقية لهذه الأداة.\n\nهل تريد استخدام واحدة منها؟")
            .setPositiveButton("استخدام", (dialog, which) -> {
                proManager.useFreeTrial(toolName);
                launchTool(tool);
                proManager.incrementToolUsage(toolName);
            })
            .setNegativeButton("مشاهدة إعلان", (dialog, which) -> {
                showRewardedAdForTool(tool);
            })
            .setNeutralButton("إلغاء", null)
            .show();
    }

    private void showRewardedAdForTool(ToolItem tool) {
        if (adManager.isRewardedAdReady()) {
            adManager.showRewardedAd(requireActivity(), new AdManager.RewardedAdCallback() {
                @Override
                public void onAdRewarded() {
                    // Unlock tool temporarily
                    String toolName = tool.getName();
                    proManager.unlockToolTemporarily(toolName);

                    long remainingTime = proManager.getRemainingUnlockTime(toolName);
                    int minutes = (int) (remainingTime / (60 * 1000));

                    Toast.makeText(getContext(),
                        "تم فتح الأداة لمدة " + minutes + " دقيقة!",
                        Toast.LENGTH_LONG).show();

                    launchTool(tool);
                    proManager.incrementToolUsage(toolName);
                }

                @Override
                public void onAdFailed() {
                    Toast.makeText(getContext(), "فشل في تحميل الإعلان، حاول مرة أخرى", Toast.LENGTH_SHORT).show();
                }
            });
        } else {
            Toast.makeText(getContext(), "الإعلان غير متاح حالياً، حاول مرة أخرى", Toast.LENGTH_SHORT).show();
        }
    }

    private void launchTool(ToolItem tool) {
        Intent intent = null;

        switch (tool.getName()) {
            case "Device Info":
                intent = new Intent(getContext(), DeviceInfoActivity.class);
                break;
            case "Ping Test":
                intent = new Intent(getContext(), PingTestActivity.class);
                break;
            case "Clear Cache":
                intent = new Intent(getContext(), ClearCacheActivity.class);
                break;
            case "Game Booster":
                Toast.makeText(getContext(), "محسن الألعاب متاح في الصفحة الرئيسية", Toast.LENGTH_SHORT).show();
                return;
            case "Basic Sensitivity":
                Toast.makeText(getContext(), "أداة الحساسية الأساسية قيد التطوير", Toast.LENGTH_SHORT).show();
                return;
            default:
                Toast.makeText(getContext(), "هذه الأداة قيد التطوير", Toast.LENGTH_SHORT).show();
                return;
        }

        if (intent != null) {
            startActivity(intent);
        }
    }
}
