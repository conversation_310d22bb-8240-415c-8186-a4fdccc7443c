#Sat Aug 16 16:50:21 EET 2025
com.mahmoudffyt.Gfx_booster.app-main-57\:/color/bottom_nav_color_selector.xml=C\:\\Users\\SST\\AndroidStudioProjects\\Gfxbooster\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\color_bottom_nav_color_selector.xml.flat
com.mahmoudffyt.Gfx_booster.app-main-57\:/drawable/card_background.xml=C\:\\Users\\SST\\AndroidStudioProjects\\Gfxbooster\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_card_background.xml.flat
com.mahmoudffyt.Gfx_booster.app-main-57\:/drawable/gradient_background.xml=C\:\\Users\\SST\\AndroidStudioProjects\\Gfxbooster\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_gradient_background.xml.flat
com.mahmoudffyt.Gfx_booster.app-main-57\:/drawable/ic_device_info.xml=C\:\\Users\\SST\\AndroidStudioProjects\\Gfxbooster\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_device_info.xml.flat
com.mahmoudffyt.Gfx_booster.app-main-57\:/drawable/ic_game_controller.xml=C\:\\Users\\SST\\AndroidStudioProjects\\Gfxbooster\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_game_controller.xml.flat
com.mahmoudffyt.Gfx_booster.app-main-57\:/drawable/ic_home.xml=C\:\\Users\\SST\\AndroidStudioProjects\\Gfxbooster\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_home.xml.flat
com.mahmoudffyt.Gfx_booster.app-main-57\:/drawable/ic_launcher_background.xml=C\:\\Users\\SST\\AndroidStudioProjects\\Gfxbooster\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_background.xml.flat
com.mahmoudffyt.Gfx_booster.app-main-57\:/drawable/ic_launcher_foreground.xml=C\:\\Users\\SST\\AndroidStudioProjects\\Gfxbooster\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_foreground.xml.flat
com.mahmoudffyt.Gfx_booster.app-main-57\:/drawable/ic_pro_crown.xml=C\:\\Users\\SST\\AndroidStudioProjects\\Gfxbooster\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_pro_crown.xml.flat
com.mahmoudffyt.Gfx_booster.app-main-57\:/drawable/ic_tips.xml=C\:\\Users\\SST\\AndroidStudioProjects\\Gfxbooster\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_tips.xml.flat
com.mahmoudffyt.Gfx_booster.app-main-57\:/drawable/ic_tools.xml=C\:\\Users\\SST\\AndroidStudioProjects\\Gfxbooster\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_tools.xml.flat
com.mahmoudffyt.Gfx_booster.app-main-57\:/drawable/pro_card_background.xml=C\:\\Users\\SST\\AndroidStudioProjects\\Gfxbooster\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_pro_card_background.xml.flat
com.mahmoudffyt.Gfx_booster.app-main-57\:/drawable/pro_label_background.xml=C\:\\Users\\SST\\AndroidStudioProjects\\Gfxbooster\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_pro_label_background.xml.flat
com.mahmoudffyt.Gfx_booster.app-main-57\:/drawable/tip_background.xml=C\:\\Users\\SST\\AndroidStudioProjects\\Gfxbooster\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_tip_background.xml.flat
com.mahmoudffyt.Gfx_booster.app-main-57\:/menu/bottom_navigation_menu.xml=C\:\\Users\\SST\\AndroidStudioProjects\\Gfxbooster\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_bottom_navigation_menu.xml.flat
com.mahmoudffyt.Gfx_booster.app-main-57\:/mipmap-anydpi/ic_launcher.xml=C\:\\Users\\SST\\AndroidStudioProjects\\Gfxbooster\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi_ic_launcher.xml.flat
com.mahmoudffyt.Gfx_booster.app-main-57\:/mipmap-anydpi/ic_launcher_round.xml=C\:\\Users\\SST\\AndroidStudioProjects\\Gfxbooster\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi_ic_launcher_round.xml.flat
com.mahmoudffyt.Gfx_booster.app-main-57\:/mipmap-hdpi/ic_launcher.webp=C\:\\Users\\SST\\AndroidStudioProjects\\Gfxbooster\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.webp.flat
com.mahmoudffyt.Gfx_booster.app-main-57\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\Users\\SST\\AndroidStudioProjects\\Gfxbooster\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_round.webp.flat
com.mahmoudffyt.Gfx_booster.app-main-57\:/mipmap-mdpi/ic_launcher.webp=C\:\\Users\\SST\\AndroidStudioProjects\\Gfxbooster\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.webp.flat
com.mahmoudffyt.Gfx_booster.app-main-57\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\Users\\SST\\AndroidStudioProjects\\Gfxbooster\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_round.webp.flat
com.mahmoudffyt.Gfx_booster.app-main-57\:/mipmap-xhdpi/ic_launcher.webp=C\:\\Users\\SST\\AndroidStudioProjects\\Gfxbooster\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.webp.flat
com.mahmoudffyt.Gfx_booster.app-main-57\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\Users\\SST\\AndroidStudioProjects\\Gfxbooster\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.mahmoudffyt.Gfx_booster.app-main-57\:/mipmap-xxhdpi/ic_launcher.webp=C\:\\Users\\SST\\AndroidStudioProjects\\Gfxbooster\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.webp.flat
com.mahmoudffyt.Gfx_booster.app-main-57\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\Users\\SST\\AndroidStudioProjects\\Gfxbooster\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.mahmoudffyt.Gfx_booster.app-main-57\:/mipmap-xxxhdpi/ic_launcher.webp=C\:\\Users\\SST\\AndroidStudioProjects\\Gfxbooster\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.mahmoudffyt.Gfx_booster.app-main-57\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\Users\\SST\\AndroidStudioProjects\\Gfxbooster\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.mahmoudffyt.Gfx_booster.app-main-57\:/xml/backup_rules.xml=C\:\\Users\\SST\\AndroidStudioProjects\\Gfxbooster\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_backup_rules.xml.flat
com.mahmoudffyt.Gfx_booster.app-main-57\:/xml/data_extraction_rules.xml=C\:\\Users\\SST\\AndroidStudioProjects\\Gfxbooster\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_data_extraction_rules.xml.flat
com.mahmoudffyt.Gfx_booster.app-mergeDebugResources-54\:/layout/activity_clear_cache.xml=C\:\\Users\\SST\\AndroidStudioProjects\\Gfxbooster\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_clear_cache.xml.flat
com.mahmoudffyt.Gfx_booster.app-mergeDebugResources-54\:/layout/activity_device_info.xml=C\:\\Users\\SST\\AndroidStudioProjects\\Gfxbooster\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_device_info.xml.flat
com.mahmoudffyt.Gfx_booster.app-mergeDebugResources-54\:/layout/activity_main.xml=C\:\\Users\\SST\\AndroidStudioProjects\\Gfxbooster\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_main.xml.flat
com.mahmoudffyt.Gfx_booster.app-mergeDebugResources-54\:/layout/activity_ping_test.xml=C\:\\Users\\SST\\AndroidStudioProjects\\Gfxbooster\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_ping_test.xml.flat
com.mahmoudffyt.Gfx_booster.app-mergeDebugResources-54\:/layout/activity_splash.xml=C\:\\Users\\SST\\AndroidStudioProjects\\Gfxbooster\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_splash.xml.flat
com.mahmoudffyt.Gfx_booster.app-mergeDebugResources-54\:/layout/card_device_status.xml=C\:\\Users\\SST\\AndroidStudioProjects\\Gfxbooster\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_card_device_status.xml.flat
com.mahmoudffyt.Gfx_booster.app-mergeDebugResources-54\:/layout/dialog_loading.xml=C\:\\Users\\SST\\AndroidStudioProjects\\Gfxbooster\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_loading.xml.flat
com.mahmoudffyt.Gfx_booster.app-mergeDebugResources-54\:/layout/fragment_home.xml=C\:\\Users\\SST\\AndroidStudioProjects\\Gfxbooster\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_home.xml.flat
com.mahmoudffyt.Gfx_booster.app-mergeDebugResources-54\:/layout/fragment_tools.xml=C\:\\Users\\SST\\AndroidStudioProjects\\Gfxbooster\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_tools.xml.flat
com.mahmoudffyt.Gfx_booster.app-mergeDebugResources-54\:/layout/item_tool.xml=C\:\\Users\\SST\\AndroidStudioProjects\\Gfxbooster\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_tool.xml.flat
