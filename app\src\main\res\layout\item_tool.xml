<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/tool_card"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    style="@style/CustomCardView"
    android:layout_margin="8dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- Pro Label -->
        <TextView
            android:id="@+id/pro_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_alignParentEnd="true"
            android:text="@string/pro_label"
            android:background="@drawable/pro_label_background"
            android:textColor="@color/text_primary"
            android:textSize="10sp"
            android:fontFamily="sans-serif-medium"
            android:paddingHorizontal="6dp"
            android:paddingVertical="2dp"
            android:visibility="gone" />

        <!-- Tool Content -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_centerInParent="true">

            <!-- Tool Icon -->
            <ImageView
                android:id="@+id/tool_icon"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_tools"
                android:layout_marginBottom="12dp"
                app:tint="@color/accent_cyan" />

            <!-- Tool Name -->
            <TextView
                android:id="@+id/tool_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Tool Name"
                style="@style/SubheadText"
                android:gravity="center"
                android:maxLines="2"
                android:ellipsize="end" />

        </LinearLayout>

    </RelativeLayout>

</androidx.cardview.widget.CardView>
