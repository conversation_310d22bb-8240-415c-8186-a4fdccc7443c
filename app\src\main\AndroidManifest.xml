<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Permissions for device info and ads -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS"
        tools:ignore="ProtectedPermissions" />

    <!-- AdMob App ID -->
    <meta-data
        android:name="com.google.android.gms.ads.APPLICATION_ID"
        android:value="ca-app-pub-3940256099942544~3347511713"/>

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.GfxBooster"
        tools:targetApi="31">

        <activity
            android:name=".SplashActivity"
            android:exported="true"
            android:theme="@style/Theme.GfxBooster">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".MainActivity"
            android:exported="false"
            android:theme="@style/Theme.GfxBooster" />

        <!-- Tools Activities -->
        <activity
            android:name=".tools.DeviceInfoActivity"
            android:exported="false"
            android:theme="@style/Theme.GfxBooster"
            android:parentActivityName=".MainActivity" />

        <activity
            android:name=".tools.PingTestActivity"
            android:exported="false"
            android:theme="@style/Theme.GfxBooster"
            android:parentActivityName=".MainActivity" />

        <activity
            android:name=".tools.ClearCacheActivity"
            android:exported="false"
            android:theme="@style/Theme.GfxBooster"
            android:parentActivityName=".MainActivity" />

    </application>

</manifest>