<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_primary">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary_blue"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    <!-- Content -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- RAM Info Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                style="@style/CustomCardView"
                android:layout_marginBottom="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="معلومات الذاكرة"
                        style="@style/HeadlineText"
                        android:layout_marginBottom="12dp"
                        android:drawableStart="@drawable/ic_device_info"
                        android:drawablePadding="8dp"
                        android:gravity="center_vertical" />

                    <TextView
                        android:id="@+id/ram_info_text"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="جاري تحميل معلومات الذاكرة..."
                        style="@style/BodyText"
                        android:fontFamily="monospace" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Cache Info Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                style="@style/CustomCardView"
                android:layout_marginBottom="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="معلومات التخزين المؤقت"
                        style="@style/HeadlineText"
                        android:layout_marginBottom="12dp"
                        android:drawableStart="@drawable/ic_tools"
                        android:drawablePadding="8dp"
                        android:gravity="center_vertical" />

                    <TextView
                        android:id="@+id/cache_info_text"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="جاري تحميل معلومات التخزين المؤقت..."
                        style="@style/BodyText"
                        android:fontFamily="monospace" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Progress Bar -->
            <ProgressBar
                android:id="@+id/progress_bar"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:visibility="gone"
                android:progressTint="@color/accent_cyan"
                android:progressBackgroundTint="@color/background_tertiary" />

            <!-- Clear Cache Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/clear_cache_button"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="مسح التخزين المؤقت"
                style="@style/PrimaryButton"
                android:layout_marginBottom="16dp" />

            <!-- Info Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                style="@style/CustomCardView">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="معلومات مهمة"
                        style="@style/HeadlineText"
                        android:layout_marginBottom="12dp"
                        android:drawableStart="@drawable/ic_tips"
                        android:drawablePadding="8dp"
                        android:gravity="center_vertical"
                        android:textColor="@color/warning_orange" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="• مسح التخزين المؤقت يساعد في تحرير مساحة الذاكرة\n• قد تحتاج بعض التطبيقات لإعادة تحميل البيانات\n• يُنصح بإجراء هذه العملية بانتظام\n• لن يؤثر على بياناتك الشخصية"
                        style="@style/BodyText"
                        android:lineSpacingExtra="4dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </ScrollView>

</LinearLayout>
