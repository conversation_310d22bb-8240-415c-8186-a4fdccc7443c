package com.mahmoudffyt.Gfx_booster.fragments;

import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import com.mahmoudffyt.Gfx_booster.R;
import com.mahmoudffyt.Gfx_booster.ads.AdManager;
import com.mahmoudffyt.Gfx_booster.utils.DeviceInfoManager;

public class HomeFragment extends Fragment {

    private DeviceInfoManager deviceInfoManager;
    private AdManager adManager;
    private Handler updateHandler;
    private Runnable updateRunnable;

    // Device Info Views
    private TextView deviceModelText;
    private TextView androidVersionText;
    private TextView totalRamText;

    // Device Status Views
    private ImageView batteryIcon;
    private TextView batteryValue;
    private ImageView ramIcon;
    private TextView ramValue;
    private ImageView cpuIcon;
    private TextView cpuValue;
    private ImageView networkIcon;
    private TextView networkValue;

    // Banner Ad Container
    private FrameLayout bannerAdContainer;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_home, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // Initialize managers
        deviceInfoManager = new DeviceInfoManager(requireContext());
        adManager = AdManager.getInstance(requireContext());

        // Initialize views and setup functionality
        initViews(view);
        setupDeviceStatus();
        setupDeviceInfo();
        setupGameBooster();
        setupBannerAd();
        startPeriodicUpdates();
    }

    private void initViews(View view) {
        // Device Info Views
        deviceModelText = view.findViewById(R.id.device_model);
        androidVersionText = view.findViewById(R.id.android_version);
        totalRamText = view.findViewById(R.id.total_ram);

        // Device Status Views
        batteryIcon = view.findViewById(R.id.battery_icon);
        batteryValue = view.findViewById(R.id.battery_value);
        ramIcon = view.findViewById(R.id.ram_icon);
        ramValue = view.findViewById(R.id.ram_value);
        cpuIcon = view.findViewById(R.id.cpu_icon);
        cpuValue = view.findViewById(R.id.cpu_value);
        networkIcon = view.findViewById(R.id.network_icon);
        networkValue = view.findViewById(R.id.network_value);

        // Banner Ad Container
        bannerAdContainer = view.findViewById(R.id.banner_ad_container);
    }

    private void setupDeviceStatus() {
        // This will be implemented with actual device status cards
        // For now, we'll update them periodically
    }

    private void setupDeviceInfo() {
        // Set static device information
        deviceModelText.setText(deviceInfoManager.getDeviceModel());
        androidVersionText.setText(deviceInfoManager.getAndroidVersion());
        totalRamText.setText("RAM: " + deviceInfoManager.getTotalRAM());
    }

    private void setupGameBooster() {
        // Setup game booster functionality
        // This will be implemented in the next phase
    }

    private void setupBannerAd() {
        if (bannerAdContainer != null) {
            adManager.loadBannerAd(bannerAdContainer);
        }
    }

    private void startPeriodicUpdates() {
        updateHandler = new Handler(Looper.getMainLooper());
        updateRunnable = new Runnable() {
            @Override
            public void run() {
                updateDeviceStatus();
                updateHandler.postDelayed(this, 5000); // Update every 5 seconds
            }
        };
        updateHandler.post(updateRunnable);
    }

    private void updateDeviceStatus() {
        if (deviceInfoManager == null) return;

        // Update Battery Status
        int batteryLevel = deviceInfoManager.getBatteryLevel();
        batteryValue.setText(batteryLevel + "%");

        int batteryColor = deviceInfoManager.getColorForPercentage(batteryLevel,
                ContextCompat.getColor(requireContext(), R.color.battery_good),
                ContextCompat.getColor(requireContext(), R.color.battery_medium),
                ContextCompat.getColor(requireContext(), R.color.battery_low));
        batteryIcon.setColorFilter(batteryColor);

        // Update RAM Status
        String availableRam = deviceInfoManager.getAvailableRAM();
        ramValue.setText(availableRam);

        int ramUsage = deviceInfoManager.getRAMUsagePercentage();
        int ramColor = deviceInfoManager.getColorForPercentage(100 - ramUsage, // Invert for available RAM
                ContextCompat.getColor(requireContext(), R.color.ram_good),
                ContextCompat.getColor(requireContext(), R.color.ram_medium),
                ContextCompat.getColor(requireContext(), R.color.ram_high));
        ramIcon.setColorFilter(ramColor);

        // Update CPU Status
        String cpuUsage = deviceInfoManager.getCPUUsage();
        cpuValue.setText(cpuUsage);

        // Update Network Status
        String networkType = deviceInfoManager.getNetworkType();
        networkValue.setText(networkType);

        boolean isConnected = deviceInfoManager.isNetworkConnected();
        int networkColor = isConnected ?
                ContextCompat.getColor(requireContext(), R.color.accent_cyan) :
                ContextCompat.getColor(requireContext(), R.color.error_red);
        networkIcon.setColorFilter(networkColor);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (updateHandler != null && updateRunnable != null) {
            updateHandler.removeCallbacks(updateRunnable);
        }
    }
}
