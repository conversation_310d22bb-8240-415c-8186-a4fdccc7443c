<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/gradient_background">

    <!-- App Logo -->
    <ImageView
        android:id="@+id/app_logo"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:src="@mipmap/ic_launcher"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/app_name"
        app:layout_constraintVertical_chainStyle="packed" />

    <!-- App Name -->
    <TextView
        android:id="@+id/app_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/app_name"
        android:textSize="32sp"
        android:textColor="@color/text_primary"
        android:fontFamily="sans-serif-medium"
        android:layout_marginTop="24dp"
        app:layout_constraintTop_toBottomOf="@+id/app_logo"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/loading_progress" />

    <!-- Loading Progress -->
    <com.google.android.material.progressindicator.LinearProgressIndicator
        android:id="@+id/loading_progress"
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:indeterminate="true"
        app:indicatorColor="@color/accent_cyan"
        app:trackColor="@color/background_tertiary"
        android:layout_marginTop="32dp"
        app:layout_constraintTop_toBottomOf="@+id/app_name"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/loading_text" />

    <!-- Loading Text -->
    <TextView
        android:id="@+id/loading_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/loading"
        style="@style/SubheadText"
        android:layout_marginTop="16dp"
        app:layout_constraintTop_toBottomOf="@+id/loading_progress"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/tip_container" />

    <!-- Tip Container -->
    <androidx.cardview.widget.CardView
        android:id="@+id/tip_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        style="@style/CustomCardView"
        android:layout_margin="32dp"
        android:layout_marginTop="48dp"
        app:layout_constraintTop_toBottomOf="@+id/loading_text"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="16dp"
            android:gravity="center_vertical">

            <!-- Tip Icon -->
            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_tips"
                app:tint="@color/accent_cyan"
                android:layout_marginEnd="12dp" />

            <!-- Tip Text -->
            <TextView
                android:id="@+id/tip_text"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/tip_close_apps"
                style="@style/BodyText"
                android:textColor="@color/text_secondary" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout>
