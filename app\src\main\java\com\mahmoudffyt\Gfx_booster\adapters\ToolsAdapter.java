package com.mahmoudffyt.Gfx_booster.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;
import com.mahmoudffyt.Gfx_booster.R;
import com.mahmoudffyt.Gfx_booster.models.ToolItem;
import java.util.List;

public class ToolsAdapter extends RecyclerView.Adapter<ToolsAdapter.ToolViewHolder> {

    private List<ToolItem> tools;
    private boolean isProSection;
    private OnToolClickListener listener;

    public interface OnToolClickListener {
        void onToolClick(ToolItem tool);
    }

    public ToolsAdapter(List<ToolItem> tools, boolean isProSection) {
        this.tools = tools;
        this.isProSection = isProSection;
    }

    public void setOnToolClickListener(OnToolClickListener listener) {
        this.listener = listener;
    }

    @NonNull
    @Override
    public ToolViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_tool, parent, false);
        return new ToolViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ToolViewHolder holder, int position) {
        ToolItem tool = tools.get(position);
        holder.bind(tool);
    }

    @Override
    public int getItemCount() {
        return tools.size();
    }

    class ToolViewHolder extends RecyclerView.ViewHolder {
        private CardView cardView;
        private ImageView iconImageView;
        private TextView nameTextView;
        private TextView proLabel;

        public ToolViewHolder(@NonNull View itemView) {
            super(itemView);
            cardView = itemView.findViewById(R.id.tool_card);
            iconImageView = itemView.findViewById(R.id.tool_icon);
            nameTextView = itemView.findViewById(R.id.tool_name);
            proLabel = itemView.findViewById(R.id.pro_label);
        }

        public void bind(ToolItem tool) {
            nameTextView.setText(tool.getName());
            iconImageView.setImageResource(tool.getIconResource());
            
            // Show/hide pro label
            if (tool.isPro()) {
                proLabel.setVisibility(View.VISIBLE);
                cardView.setBackgroundResource(R.drawable.pro_card_background);
            } else {
                proLabel.setVisibility(View.GONE);
                cardView.setBackgroundResource(R.drawable.card_background);
            }

            // Set click listener
            cardView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onToolClick(tool);
                }
            });
        }
    }
}
