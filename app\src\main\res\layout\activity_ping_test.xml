<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_primary">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary_blue"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    <!-- Content -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Info Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            style="@style/CustomCardView"
            android:layout_marginBottom="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="اختبار سرعة الاتصال"
                    style="@style/HeadlineText"
                    android:layout_marginBottom="8dp"
                    android:drawableStart="@drawable/ic_tools"
                    android:drawablePadding="8dp"
                    android:gravity="center_vertical" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="اختبر سرعة الاتصال بالإنترنت لديك مع خوادم مختلفة"
                    style="@style/BodyText" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Test Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/start_test_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="بدء الاختبار"
            style="@style/PrimaryButton"
            android:layout_marginBottom="16dp" />

        <!-- Results Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            style="@style/CustomCardView">

            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="16dp">

                <TextView
                    android:id="@+id/ping_result_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="اضغط على 'بدء الاختبار' لاختبار سرعة الاتصال"
                    style="@style/BodyText"
                    android:fontFamily="monospace"
                    android:textIsSelectable="true" />

            </ScrollView>

        </androidx.cardview.widget.CardView>

    </LinearLayout>

</LinearLayout>
