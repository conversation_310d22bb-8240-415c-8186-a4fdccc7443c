package com.mahmoudffyt.Gfx_booster.ads;

import android.app.Activity;
import android.content.Context;
import android.util.Log;
import android.widget.FrameLayout;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdView;
import com.google.android.gms.ads.LoadAdError;
import com.google.android.gms.ads.MobileAds;
import com.google.android.gms.ads.interstitial.InterstitialAd;
import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback;
import com.google.android.gms.ads.rewarded.RewardedAd;
import com.google.android.gms.ads.rewarded.RewardedAdLoadCallback;
import com.google.android.gms.ads.AdSize;
import androidx.annotation.NonNull;

public class AdManager {
    
    private static final String TAG = "AdManager";
    
    // Test Ad Unit IDs - Replace with your actual Ad Unit IDs
    private static final String BANNER_AD_UNIT_ID = "ca-app-pub-3940256099942544/6300978111";
    private static final String INTERSTITIAL_AD_UNIT_ID = "ca-app-pub-3940256099942544/1033173712";
    private static final String REWARDED_AD_UNIT_ID = "ca-app-pub-3940256099942544/5224354917";
    
    private static AdManager instance;
    private Context context;
    private InterstitialAd interstitialAd;
    private RewardedAd rewardedAd;
    private long lastInterstitialTime = 0;
    private static final long INTERSTITIAL_COOLDOWN = 5 * 60 * 1000; // 5 minutes
    
    public interface RewardedAdCallback {
        void onAdRewarded();
        void onAdFailed();
    }
    
    public interface InterstitialAdCallback {
        void onAdClosed();
        void onAdFailed();
    }

    private AdManager(Context context) {
        this.context = context.getApplicationContext();
        initializeAds();
    }

    public static synchronized AdManager getInstance(Context context) {
        if (instance == null) {
            instance = new AdManager(context);
        }
        return instance;
    }

    private void initializeAds() {
        MobileAds.initialize(context, initializationStatus -> {
            Log.d(TAG, "AdMob initialized successfully");
            loadInterstitialAd();
            loadRewardedAd();
        });
    }

    public void loadBannerAd(FrameLayout adContainer) {
        AdView adView = new AdView(context);
        adView.setAdUnitId(BANNER_AD_UNIT_ID);
        adView.setAdSize(AdSize.BANNER);
        
        AdRequest adRequest = new AdRequest.Builder().build();
        adView.loadAd(adRequest);
        
        adContainer.removeAllViews();
        adContainer.addView(adView);
    }

    private void loadInterstitialAd() {
        AdRequest adRequest = new AdRequest.Builder().build();
        
        InterstitialAd.load(context, INTERSTITIAL_AD_UNIT_ID, adRequest,
            new InterstitialAdLoadCallback() {
                @Override
                public void onAdLoaded(@NonNull InterstitialAd ad) {
                    interstitialAd = ad;
                    Log.d(TAG, "Interstitial ad loaded");
                }

                @Override
                public void onAdFailedToLoad(@NonNull LoadAdError loadAdError) {
                    Log.e(TAG, "Interstitial ad failed to load: " + loadAdError.getMessage());
                    interstitialAd = null;
                }
            });
    }

    private void loadRewardedAd() {
        AdRequest adRequest = new AdRequest.Builder().build();
        
        RewardedAd.load(context, REWARDED_AD_UNIT_ID, adRequest,
            new RewardedAdLoadCallback() {
                @Override
                public void onAdLoaded(@NonNull RewardedAd ad) {
                    rewardedAd = ad;
                    Log.d(TAG, "Rewarded ad loaded");
                }

                @Override
                public void onAdFailedToLoad(@NonNull LoadAdError loadAdError) {
                    Log.e(TAG, "Rewarded ad failed to load: " + loadAdError.getMessage());
                    rewardedAd = null;
                }
            });
    }

    public void showInterstitialAd(Activity activity, InterstitialAdCallback callback) {
        long currentTime = System.currentTimeMillis();
        
        // Check cooldown period
        if (currentTime - lastInterstitialTime < INTERSTITIAL_COOLDOWN) {
            Log.d(TAG, "Interstitial ad on cooldown");
            if (callback != null) callback.onAdClosed();
            return;
        }
        
        if (interstitialAd != null) {
            interstitialAd.show(activity);
            lastInterstitialTime = currentTime;
            
            interstitialAd.setFullScreenContentCallback(new com.google.android.gms.ads.FullScreenContentCallback() {
                @Override
                public void onAdDismissedFullScreenContent() {
                    interstitialAd = null;
                    loadInterstitialAd(); // Load next ad
                    if (callback != null) callback.onAdClosed();
                }

                @Override
                public void onAdFailedToShowFullScreenContent(@NonNull com.google.android.gms.ads.AdError adError) {
                    interstitialAd = null;
                    if (callback != null) callback.onAdFailed();
                }
            });
        } else {
            Log.d(TAG, "Interstitial ad not ready");
            if (callback != null) callback.onAdFailed();
        }
    }

    public void showRewardedAd(Activity activity, RewardedAdCallback callback) {
        if (rewardedAd != null) {
            rewardedAd.show(activity, rewardItem -> {
                Log.d(TAG, "User earned reward: " + rewardItem.getAmount());
                if (callback != null) callback.onAdRewarded();
            });
            
            rewardedAd.setFullScreenContentCallback(new com.google.android.gms.ads.FullScreenContentCallback() {
                @Override
                public void onAdDismissedFullScreenContent() {
                    rewardedAd = null;
                    loadRewardedAd(); // Load next ad
                }

                @Override
                public void onAdFailedToShowFullScreenContent(@NonNull com.google.android.gms.ads.AdError adError) {
                    rewardedAd = null;
                    if (callback != null) callback.onAdFailed();
                }
            });
        } else {
            Log.d(TAG, "Rewarded ad not ready");
            if (callback != null) callback.onAdFailed();
        }
    }

    public boolean isInterstitialReady() {
        return interstitialAd != null;
    }

    public boolean isRewardedAdReady() {
        return rewardedAd != null;
    }
}
