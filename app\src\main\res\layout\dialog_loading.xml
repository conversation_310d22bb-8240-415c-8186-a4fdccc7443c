<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="280dp"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    style="@style/CustomCardView">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp"
        android:gravity="center">

        <!-- Loading Animation -->
        <com.google.android.material.progressindicator.CircularProgressIndicator
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:indeterminate="true"
            app:indicatorColor="@color/accent_cyan"
            app:trackColor="@color/background_tertiary"
            android:layout_marginBottom="16dp" />

        <!-- Loading Text -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/loading"
            style="@style/SubheadText"
            android:layout_marginBottom="16dp" />

        <!-- Tip Container -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:background="@drawable/tip_background"
            android:padding="12dp">

            <!-- Tip Icon -->
            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_tips"
                app:tint="@color/accent_cyan"
                android:layout_marginEnd="8dp" />

            <!-- Tip Text -->
            <TextView
                android:id="@+id/tip_text"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/tip_close_apps"
                style="@style/CaptionText"
                android:textColor="@color/text_secondary" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
