plugins {
    alias(libs.plugins.android.application)
}

android {
    namespace 'com.mahmoudffyt.Gfx_booster'
    compileSdk 35

    defaultConfig {
        applicationId "com.mahmoudffyt.Gfx_booster"
        minSdk 28
        targetSdk 35
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    buildFeatures {
        viewBinding true
    }
}

dependencies {
    // Core Android libraries
    implementation libs.appcompat
    implementation libs.material
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation 'androidx.recyclerview:recyclerview:1.3.2'

    // Navigation Components
    implementation 'androidx.navigation:navigation-fragment:2.7.6'
    implementation 'androidx.navigation:navigation-ui:2.7.6'

    // ViewPager2 for fragments
    implementation 'androidx.viewpager2:viewpager2:1.0.0'

    // Lifecycle components
    implementation 'androidx.lifecycle:lifecycle-viewmodel:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-livedata:2.7.0'

    // Google Play Services for AdMob
    implementation 'com.google.android.gms:play-services-ads:22.6.0'

    // Permissions handling
    implementation 'androidx.activity:activity:1.8.2'
    implementation 'androidx.fragment:fragment:1.6.2'

    // Gson for JSON parsing
    implementation 'com.google.code.gson:gson:2.10.1'

    // Shared Preferences
    implementation 'androidx.preference:preference:1.2.1'

    // Lottie for animations
    implementation 'com.airbnb.android:lottie:6.2.0'

    // Testing
    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core
}