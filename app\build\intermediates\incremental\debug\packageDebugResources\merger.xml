<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res"><file name="bottom_nav_color_selector" path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\color\bottom_nav_color_selector.xml" qualifiers="" type="color"/><file name="card_background" path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\drawable\card_background.xml" qualifiers="" type="drawable"/><file name="gradient_background" path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\drawable\gradient_background.xml" qualifiers="" type="drawable"/><file name="ic_device_info" path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\drawable\ic_device_info.xml" qualifiers="" type="drawable"/><file name="ic_game_controller" path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\drawable\ic_game_controller.xml" qualifiers="" type="drawable"/><file name="ic_home" path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\drawable\ic_home.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_pro_crown" path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\drawable\ic_pro_crown.xml" qualifiers="" type="drawable"/><file name="ic_tips" path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\drawable\ic_tips.xml" qualifiers="" type="drawable"/><file name="ic_tools" path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\drawable\ic_tools.xml" qualifiers="" type="drawable"/><file name="pro_card_background" path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\drawable\pro_card_background.xml" qualifiers="" type="drawable"/><file name="pro_label_background" path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\drawable\pro_label_background.xml" qualifiers="" type="drawable"/><file name="tip_background" path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\drawable\tip_background.xml" qualifiers="" type="drawable"/><file name="activity_clear_cache" path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\layout\activity_clear_cache.xml" qualifiers="" type="layout"/><file name="activity_device_info" path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\layout\activity_device_info.xml" qualifiers="" type="layout"/><file name="activity_main" path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_ping_test" path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\layout\activity_ping_test.xml" qualifiers="" type="layout"/><file name="activity_splash" path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\layout\activity_splash.xml" qualifiers="" type="layout"/><file name="card_device_status" path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\layout\card_device_status.xml" qualifiers="" type="layout"/><file name="dialog_loading" path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\layout\dialog_loading.xml" qualifiers="" type="layout"/><file name="fragment_home" path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\layout\fragment_home.xml" qualifiers="" type="layout"/><file name="fragment_tools" path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\layout\fragment_tools.xml" qualifiers="" type="layout"/><file name="item_tool" path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\layout\item_tool.xml" qualifiers="" type="layout"/><file name="bottom_navigation_menu" path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\menu\bottom_navigation_menu.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\mipmap-anydpi\ic_launcher.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\mipmap-anydpi\ic_launcher_round.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="primary_blue">#1E3A8A</color><color name="primary_blue_dark">#1E40AF</color><color name="primary_blue_light">#3B82F6</color><color name="accent_cyan">#06B6D4</color><color name="accent_cyan_dark">#0891B2</color><color name="accent_cyan_light">#67E8F9</color><color name="pro_gold">#F59E0B</color><color name="pro_gold_dark">#D97706</color><color name="pro_gold_light">#FCD34D</color><color name="background_primary">#0F172A</color><color name="background_secondary">#1E293B</color><color name="background_tertiary">#334155</color><color name="background_card">#1E293B</color><color name="background_card_elevated">#334155</color><color name="text_primary">#F8FAFC</color><color name="text_secondary">#CBD5E1</color><color name="text_tertiary">#94A3B8</color><color name="text_disabled">#64748B</color><color name="success_green">#10B981</color><color name="warning_orange">#F59E0B</color><color name="error_red">#EF4444</color><color name="info_blue">#3B82F6</color><color name="battery_good">#10B981</color><color name="battery_medium">#F59E0B</color><color name="battery_low">#EF4444</color><color name="ram_good">#06B6D4</color><color name="ram_medium">#F59E0B</color><color name="ram_high">#EF4444</color><color name="cpu_normal">#10B981</color><color name="cpu_medium">#F59E0B</color><color name="cpu_high">#EF4444</color><color name="transparent">#00000000</color><color name="semi_transparent_black">#80000000</color><color name="semi_transparent_white">#80FFFFFF</color><color name="gradient_start">#1E3A8A</color><color name="gradient_end">#06B6D4</color></file><file path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Gfx Booster</string><string name="nav_home">الرئيسية</string><string name="nav_tools">الأدوات</string><string name="device_status">حالة الجهاز</string><string name="device_info">معلومات الجهاز</string><string name="game_booster">محسن الألعاب</string><string name="add_games">إضافة ألعاب</string><string name="battery">البطارية</string><string name="ram">الذاكرة</string><string name="cpu">المعالج</string><string name="network">الشبكة</string><string name="free_tools">الأدوات المجانية</string><string name="pro_tools">الأدوات المتقدمة</string><string name="pro_label">PRO</string><string name="ping_test">اختبار الاتصال</string><string name="clear_cache">مسح التخزين المؤقت</string><string name="basic_sensitivity">الحساسية الأساسية</string><string name="smart_aim">التصويب الذكي</string><string name="crosshair_tool">أداة التصويب</string><string name="fps_counter">عداد الإطارات</string><string name="advanced_sensitivity">الحساسية المتقدمة</string><string name="graphics_control">التحكم في الرسوميات</string><string name="network_optimizer">محسن الشبكة</string><string name="performance_monitor">مراقب الأداء</string><string name="watch_ad_to_unlock">شاهد إعلان للفتح</string><string name="upgrade_to_pro">ترقية إلى Pro</string><string name="loading">جاري التحميل...</string><string name="tip_close_apps">نصيحة: أغلق التطبيقات في الخلفية لأداء أفضل</string><string name="tip_restart_device">نصيحة: أعد تشغيل الجهاز بانتظام لتحسين الأداء</string><string name="tip_clear_cache">نصيحة: امسح التخزين المؤقت بانتظام</string><string name="tip_update_games">نصيحة: حدث ألعابك للحصول على أفضل أداء</string></file><file path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.GfxBooster" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/primary_blue</item>
        <item name="colorPrimaryVariant">@color/primary_blue_dark</item>
        <item name="colorOnPrimary">@color/text_primary</item>

        
        <item name="colorSecondary">@color/accent_cyan</item>
        <item name="colorSecondaryVariant">@color/accent_cyan_dark</item>
        <item name="colorOnSecondary">@color/text_primary</item>

        
        <item name="android:colorBackground">@color/background_primary</item>
        <item name="colorSurface">@color/background_card</item>
        <item name="colorOnSurface">@color/text_primary</item>

        
        <item name="android:statusBarColor">@color/background_primary</item>
        <item name="android:windowLightStatusBar">false</item>

        
        <item name="android:navigationBarColor">@color/background_primary</item>

        
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>

        
        <item name="cardViewStyle">@style/CustomCardView</item>
    </style><style name="CustomCardView" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/background_card</item>
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">4dp</item>
        <item name="android:layout_margin">8dp</item>
    </style><style name="PrimaryButton" parent="Widget.MaterialComponents.Button">
        <item name="backgroundTint">@color/primary_blue</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style><style name="SecondaryButton" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="strokeColor">@color/accent_cyan</item>
        <item name="android:textColor">@color/accent_cyan</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style><style name="ProButton" parent="Widget.MaterialComponents.Button">
        <item name="backgroundTint">@color/pro_gold</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style><style name="HeadlineText">
        <item name="android:textSize">24sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style><style name="SubheadText">
        <item name="android:textSize">18sp</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:fontFamily">sans-serif</item>
    </style><style name="BodyText">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:fontFamily">sans-serif</item>
    </style><style name="CaptionText">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">@color/text_tertiary</item>
        <item name="android:fontFamily">sans-serif</item>
    </style></file><file path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\values-night\colors.xml" qualifiers="night-v8"><color name="background_primary">#000000</color><color name="background_secondary">#111827</color><color name="background_tertiary">#1F2937</color><color name="background_card">#111827</color><color name="background_card_elevated">#1F2937</color><color name="text_primary">#FFFFFF</color><color name="text_secondary">#E5E7EB</color><color name="text_tertiary">#9CA3AF</color><color name="text_disabled">#6B7280</color><color name="primary_blue">#3B82F6</color><color name="primary_blue_dark">#1D4ED8</color><color name="primary_blue_light">#60A5FA</color><color name="accent_cyan">#22D3EE</color><color name="accent_cyan_dark">#0891B2</color><color name="accent_cyan_light">#7DD3FC</color></file><file path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Theme.GfxBooster" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/primary_blue</item>
        <item name="colorPrimaryVariant">@color/primary_blue_dark</item>
        <item name="colorOnPrimary">@color/text_primary</item>

        
        <item name="colorSecondary">@color/accent_cyan</item>
        <item name="colorSecondaryVariant">@color/accent_cyan_dark</item>
        <item name="colorOnSecondary">@color/text_primary</item>

        
        <item name="android:colorBackground">@color/background_primary</item>
        <item name="colorSurface">@color/background_card</item>
        <item name="colorOnSurface">@color/text_primary</item>

        
        <item name="android:statusBarColor">@color/background_primary</item>
        <item name="android:windowLightStatusBar">false</item>

        
        <item name="android:navigationBarColor">@color/background_primary</item>

        
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
    </style></file><file name="backup_rules" path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\Gfxbooster\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>