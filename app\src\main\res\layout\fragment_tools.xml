<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/nav_tools"
            android:textSize="28sp"
            android:textColor="@color/text_primary"
            android:fontFamily="sans-serif-medium"
            android:gravity="center"
            android:layout_marginBottom="24dp" />

        <!-- Free Tools Section -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/free_tools"
            style="@style/HeadlineText"
            android:layout_marginBottom="12dp"
            android:drawableStart="@drawable/ic_tools"
            android:drawablePadding="8dp"
            android:gravity="center_vertical" />

        <!-- Free Tools RecyclerView -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/free_tools_recycler"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="32dp"
            android:nestedScrollingEnabled="false" />

        <!-- Pro Tools Section -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/pro_tools"
            style="@style/HeadlineText"
            android:layout_marginBottom="12dp"
            android:drawableStart="@drawable/ic_pro_crown"
            android:drawablePadding="8dp"
            android:gravity="center_vertical"
            android:textColor="@color/pro_gold" />

        <!-- Pro Tools RecyclerView -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/pro_tools_recycler"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:nestedScrollingEnabled="false" />

        <!-- Banner Ad Placeholder -->
        <FrameLayout
            android:id="@+id/banner_ad_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:background="@color/background_secondary"
            android:minHeight="50dp" />

    </LinearLayout>

</ScrollView>
